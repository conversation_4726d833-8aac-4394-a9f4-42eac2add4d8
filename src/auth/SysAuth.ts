export default {
  ROLE: {
    ADD: 'system:role:add',
    EDIT: 'system:role:edit',
    REMOVE: 'system:role:remove',
    EXPORT: 'system:role:export',
  },
  USER: {
    ADD: 'system:user:add',
    EDIT: 'system:user:edit',
    REMOVE: 'system:user:remove',
    EXPORT: 'system:user:export',
    IMPORT: 'system:user:import',
    RESET_PASSWORD: 'system:user:resetPwd',
  },
  MENU: {
    ADD: 'system:menu:add',
    EDIT: 'system:menu:edit',
    REMOVE: 'system:menu:remove',
  },
  DEPT: {
    ADD: 'system:dept:add',
    EDIT: 'system:dept:edit',
    REMOVE: 'system:dept:remove',
  },
  POST: {
    ADD: 'system:post:add',
    EDIT: 'system:post:edit',
    REMOVE: 'system:post:remove',
    EXPORT: 'system:post:export',
  },
  DICT: {
    ADD: 'system:dict:add',
    EDIT: 'system:dict:edit',
    REMOVE: 'system:dict:remove',
    EXPORT: 'system:dict:export',
  },
  CONFIG: {
    ADD: 'system:config:add',
    EDIT: 'system:config:edit',
    REMOVE: 'system:config:remove',
    EXPORT: 'system:config:export',
  },
  NOTICE: {
    ADD: 'system:notice:add',
    EDIT: 'system:notice:edit',
    REMOVE: 'system:notice:remove',
  },
  LOGIN_INFOR: {
    EXPORT: 'monitor:logininfor:export',
    REMOVE: 'monitor:logininfor:remove',
  },
  OPERLOG: {
    EXPORT: 'monitor:operlog:export',
    REMOVE: 'monitor:operlog:remove',
  },
  OSS: {
    UPLOAD: 'system:oss:upload',
    DOWNLOAD: 'system:oss:remove',
    REMOVE: 'system:oss:remove',
  },
  OSS_CONFIG: {
    ADD: 'system:ossConfig:add',
    EDIT: 'system:ossConfig:edit',
    REMOVE: 'system:ossConfig:remove',
  },
  MSGTEMPLATE: {
    ADD: 'system:msgTemplate:add',
    EDIT: 'system:msgTemplate:edit',
    REMOVE: 'system:msgTemplate:remove',
    EXPORT: 'system:msgTemplate:export',
  },
  MYWORK: {
    MENU: 'system:mywork:repair:menu',
  },
};
