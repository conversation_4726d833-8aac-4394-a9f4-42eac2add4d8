<template>
  <div class="analytics-dashboard">
    <div class="dashboard-container">
      <!-- 左侧内容区域 -->
      <div class="left-content">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>WELCOME!</h1>
              <p class="subtitle">早安，陕西省机关事务数字化管理服务保障平台</p>
              <div class="department-info">
                <span class="tag">系统管理员</span>
                <span class="tag">陕西省机关事务管理服务中心</span>
              </div>
            </div>
            <div class="welcome-image">
              <div class="illustration"></div>
            </div>
          </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-cards">
          <div class="stats-card blue">
            <div class="stats-content">
              <div class="stats-label">房屋数量（套）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+0.7%</span>
              </div>
            </div>
          </div>

          <div class="stats-card orange">
            <div class="stats-content">
              <div class="stats-label">车位数量（个）</div>
              <div class="stats-value">108</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value negative">-8%</span>
              </div>
            </div>
          </div>

          <div class="stats-card green">
            <div class="stats-content">
              <div class="stats-label">营业收入（万元）</div>
              <div class="stats-value">129.08</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+60%</span>
              </div>
            </div>
          </div>

          <div class="stats-card purple">
            <div class="stats-content">
              <div class="stats-label">月租收入（万元）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+60%</span>
              </div>
            </div>
          </div>

          <div class="stats-card red">
            <div class="stats-content">
              <div class="stats-label">欠费（万元）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value negative">-0%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 中部图表区域 -->
        <div class="middle-charts">
          <!-- 左侧图表 -->
          <div class="chart-left">
            <!-- 空置率排名 -->
            <div class="chart-card">
              <h3 class="flex items-center"
                ><div
                  style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
                ></div
                >空置率排名</h3
              >
              <div style="height: 75%; margin-top: 1%">
                <VChart :option="vacancyRateOption" style="width: 100%; height: 100%" />
              </div>
            </div>
          </div>

          <!-- 右侧图表 -->
          <div class="chart-right">
            <!-- 合同情况 -->
            <div class="chart-card">
              <h3 class="flex items-center"
                ><div
                  style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
                ></div
                >合同情况</h3
              >
            </div>
          </div>
        </div>

        <!-- 底部图表区域 -->
        <div class="bottom-charts">
          <!-- 收款类型占比 -->
          <div class="chart-card" style="height: 20vh">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >收款类型占比</h3
            >
            <div style="height: 75%; margin-top: 1%">
              <VChart :option="paymentTypeOption" style="width: 100%; height: 100%" />
            </div>
          </div>

          <!-- 小区收入统计 -->
          <div class="chart-card" style="height: 20vh">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >小区收入统计（本月）</h3
            >
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <!-- 房屋年限 -->
        <div class="chart-card" style="height: 26vh">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋年限</h3
          >
          <div style="height: 75%; margin-top: 1%">
            <VChart :option="houseAgeOption" style="width: 100%; height: 100%" />
          </div>
        </div>

        <!-- 房屋状态 -->
        <div class="chart-card" style="height: 26.5vh">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋状态</h3
          >
          <div style="height: 75%; margin-top: 1%">
            <VChart :option="houseStatusOption" style="width: 100%; height: 100%" />
          </div>
        </div>

        <!-- 支付方式占比 -->
        <div class="chart-card" style="height: 20vh">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >支付方式占比</h3
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import VChart from 'vue-echarts';
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart, BarChart } from 'echarts/charts';
  import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';

  use([CanvasRenderer, PieChart, BarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent]);

  // 房屋年限饼图数据
  const houseAgeData = ref([
    { name: '5年以下', value: 450, color: '#1890FF' },
    { name: '5-10年', value: 680, color: '#52C41A' },
    { name: '10-15年', value: 520, color: '#FA8C16' },
    { name: '15-20年', value: 380, color: '#F5222D' },
    { name: '20年以上', value: 100, color: '#722ED1' },
  ]);

  // 房屋状态环图数据
  const houseStatusData = ref([
    { name: '已出租', value: 1280, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
    { name: '空置', value: 520, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
    { name: '维修中', value: 180, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
    { name: '待出租', value: 150, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
  ]);

  // 收款类型占比环图数据
  const paymentTypeData = ref([
    { name: '银行转账', value: 1850, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
    { name: '现金收款', value: 680, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
    { name: '支付宝', value: 420, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
    { name: '微信支付', value: 380, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
    { name: '其他', value: 120, color: '#722ED1', transparentColor: 'rgba(114, 46, 209, 0.25)' },
  ]);

  // 空置率排名柱状图数据
  const vacancyRateData = ref([
    { name: '西五路\n109号院', value: 40 },
    { name: '西五路\n78号院', value: 35 },
    { name: '西五路\n82#1号院', value: 32 },
    { name: '军事院\n55号院', value: 28 },
    { name: '军事院\n51号院', value: 25 },
    { name: '军事院\n43号院', value: 22 },
    { name: '军事院\n41号院', value: 18 },
    { name: '西七路\n426号院', value: 15 },
    { name: '西五路\n141号院', value: 12 },
    { name: '西五路\n162号院', value: 8 },
  ]);

  // 房屋年限饼图配置
  const houseAgeOption = ref({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: houseAgeData.value.map((item) => item.name),
    },
    series: [
      {
        name: '房屋年限',
        type: 'pie',
        radius: '50%',
        center: ['25%', '50%'],
        data: houseAgeData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
          },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
    ],
  });

  // 房屋状态环图配置
  const houseStatusOption = ref({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: houseStatusData.value.map((item) => item.name),
    },
    series: [
      {
        name: '房屋状态',
        type: 'pie',
        radius: ['50%', '60%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: houseStatusData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
          },
        })),
      },
      {
        name: '房屋状态内环',
        type: 'pie',
        radius: ['40%', '50%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: houseStatusData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.transparentColor,
          },
        })),
      },
    ],
  });

  // 收款类型占比环图配置
  const paymentTypeOption = ref({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: paymentTypeData.value.map((item) => item.name),
    },
    series: [
      {
        name: '收款类型',
        type: 'pie',
        radius: ['50%', '60%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: paymentTypeData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
          },
        })),
      },
      {
        name: '收款类型内环',
        type: 'pie',
        radius: ['40%', '50%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: paymentTypeData.value.map((item) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.transparentColor,
          },
        })),
      },
    ],
  });

  // 空置率排名柱状图配置
  const vacancyRateOption = ref({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        return `${params[0].name}<br/>空置率: ${params[0].value}%`;
      },
    },
    graphic: [
      {
        type: 'group',
        right: 50,
        top: 30,
        children: [
          {
            type: 'rect',
            shape: {
              width: 120,
              height: 50,
              r: 8,
            },
            style: {
              fill: '#fff',
              stroke: '#ddd',
              lineWidth: 1,
              shadowBlur: 8,
              shadowColor: 'rgba(0,0,0,0.1)',
              shadowOffsetX: 2,
              shadowOffsetY: 2,
            },
          },
          {
            type: 'text',
            style: {
              text: '南长安22号',
              fontSize: 12,
              fontWeight: 'bold',
              fill: '#333',
              x: 60,
              y: 20,
              textAlign: 'center',
            },
          },
          {
            type: 'text',
            style: {
              text: '月收入：20.5万元',
              fontSize: 10,
              fill: '#666',
              x: 60,
              y: 35,
              textAlign: 'center',
            },
          },
        ],
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: vacancyRateData.value.map((item) => item.name),
      axisLabel: {
        interval: 0,
        rotate: 0,
        fontSize: 10,
        color: '#666',
        lineHeight: 12,
        rich: {
          a: {
            fontSize: 10,
            lineHeight: 12,
          },
        },
        formatter: function (value: any) {
          return value.replace('\\n', '\n');
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10,
        color: '#666',
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '空置率',
        type: 'bar',
        data: vacancyRateData.value.map((item) => item.value),
        barWidth: '60%',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#4A90E2',
              },
              {
                offset: 1,
                color: '#7BB3F0',
              },
            ],
          },
          borderRadius: [4, 4, 0, 0],
        },
        emphasis: {
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#357ABD',
                },
                {
                  offset: 1,
                  color: '#5A9BD4',
                },
              ],
            },
          },
        },
      },
    ],
  });
</script>

<style lang="less" scoped>
  /* 大屏幕优化 */
  @media (min-width: 1920px) {
    .dashboard-container {
      max-width: 1800px;
    }

    .right-content {
      max-width: 450px;
    }
  }

  /* 中等屏幕 */
  @media (max-width: 1400px) {
    .dashboard-container {
      grid-template-columns: 2.5fr 1fr;
    }

    .right-content {
      min-width: 260px;
    }
  }

  /* 小屏幕平板 */
  @media (max-width: 1200px) {
    .dashboard-container {
      grid-template-columns: 1fr;
      gap: clamp(15px, 2vw, 20px);
    }

    .right-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      width: 100%;
      min-width: 0;
      max-width: none;
      gap: clamp(15px, 2vw, 20px);
    }

    .stats-cards {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
  }

  .analytics-dashboard {
    height: 100%;
    padding: clamp(10px, 2vw, 20px);
    overflow: auto;
    background: url('@/assets/work/BG.png');
    background-position: center;
    background-size: cover;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  .dashboard-container {
    display: grid;
    grid-template-columns: 75% 25%;
    width: 100%;
    min-height: 100%;
    margin: 0 auto;
    gap: 2%;
  }

  .left-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 2%;
    min-width: 0; /* 防止flex子项溢出 */
  }

  .right-content {
    display: flex;
    flex-direction: column;
    align-items: stretch; /* 确保所有子元素高度一致 */
    width: 100%;
    gap: 2%;
  }

  .welcome-section {
    padding: 3%;
    border-radius: 12px;
    background: url('@/assets/work/welcome.png');
    background-position: center;
    background-size: cover;
    color: rgb(46 117 255 / 100%);
  }

  .welcome-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 3%;
  }

  .welcome-text {
    flex: 1;
    min-width: 60%;

    h1 {
      margin: 0 0 1%;
      font-size: clamp(24px, 4vw, 36px);
      font-weight: bold;
      line-height: 1.2;
    }
  }

  .subtitle {
    margin: 0 0 15px;
    opacity: 0.9;
    font-size: clamp(14px, 1.5vw, 16px);
    line-height: 1.4;
  }

  .department-info {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .tag {
    padding: 4px 12px;
    border-radius: 20px;
    background: rgb(255 255 255 / 20%);
    font-size: clamp(12px, 1.2vw, 14px);
    white-space: nowrap;
  }

  .welcome-image {
    flex-shrink: 0;
    width: clamp(150px, 20vw, 200px);
    height: clamp(90px, 12vw, 120px);
  }

  .illustration {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120"><rect width="200" height="120" fill="%23ffffff20"/><circle cx="100" cy="60" r="30" fill="%23ffffff40"/></svg>');
    background-size: cover;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    padding: clamp(10px, 1.5vw, 15px) 0;
    background: url('@/assets/work/中间数据背景.png');
    background-size: 100% 100%;
    gap: clamp(5px, 1vw, 10px);

    @media (min-width: 1200px) {
      grid-template-columns: repeat(5, 1fr);
      gap: 0;
    }

    .stats-card {
      min-width: 0; /* 防止内容溢出 */
      padding: clamp(10px, 1.5vw, 20px);

      .stats-icon {
        margin-bottom: 15px;
        font-size: clamp(20px, 2vw, 24px);
      }

      .stats-content {
        .stats-label {
          margin-bottom: 8px;
          color: #666;
          font-size: clamp(12px, 1.2vw, 14px);
          line-height: 1.3;
        }

        .stats-value {
          margin-bottom: 8px;
          font-size: clamp(20px, 2.5vw, 28px);
          font-weight: bold;
          line-height: 1.2;
        }

        .stats-change {
          display: flex;
          justify-content: space-between;
          font-size: clamp(10px, 1vw, 12px);
          gap: 5px;

          .change-text {
            flex-shrink: 0;
            color: #999;
          }

          .change-value {
            font-weight: bold;
            text-align: right;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }
          }
        }
      }

      &.blue {
        .stats-icon {
          color: #1890ff;
        }

        .stats-value {
          color: #1890ff;
        }
      }

      &.orange {
        .stats-icon {
          color: #fa8c16;
        }

        .stats-value {
          color: #fa8c16;
        }
      }

      &.green {
        .stats-icon {
          color: #52c41a;
        }

        .stats-value {
          color: #52c41a;
        }
      }

      &.purple {
        .stats-icon {
          color: #722ed1;
        }

        .stats-value {
          color: #722ed1;
        }
      }

      &.red {
        .stats-icon {
          color: #ff4d4f;
        }

        .stats-value {
          color: #ff4d4f;
        }
      }
    }
  }

  .middle-charts,
  .bottom-charts {
    display: grid;
    gap: clamp(10px, 1.5vw, 15px);

    .chart-left,
    .chart-right {
      min-width: 0; /* 防止内容溢出 */
    }
  }

  .middle-charts {
    grid-template-columns: 2fr 1fr;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }
  }

  .bottom-charts {
    grid-template-columns: 1fr 1fr;
    align-items: stretch; /* 确保所有子元素高度一致 */

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }

    .chart-card {
      height: 25vh;
    }
  }

  .chart-card {
    display: flex;
    flex-direction: column;
    min-width: 0; /* 防止内容溢出 */
    height: 25vh;
    padding: clamp(15px, 2vw, 20px);
    border-radius: 12px;
    background: white;
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);

    h3 {
      flex-shrink: 0;
      margin-bottom: clamp(10px, 1vw, 15px);
      font-size: clamp(14px, 1.5vw, 16px);
    }

    :deep(.ant-card-head) {
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-size: clamp(14px, 1.5vw, 16px);
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      flex: 1;
      padding: 0;
    }
  }

  .bar-chart {
    .chart-placeholder {
      display: flex;
      align-items: end;
      justify-content: space-between;
      height: 200px;
      padding: 20px 0;

      .bar-item {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        margin: 0 2px;

        .bar {
          width: 20px;
          min-height: 10px;
          margin-bottom: 8px;
          border-radius: 4px 4px 0 0;
          background: linear-gradient(to top, #1890ff, #69c0ff);
        }

        .bar-label {
          color: #666;
          font-size: 12px;
          text-align: center;
          writing-mode: vertical-rl;
          text-orientation: mixed;
        }
      }
    }
  }

  .pie-chart {
    display: flex;
    align-items: center;
    gap: 20px;

    .pie-placeholder {
      display: flex;
      position: relative;
      flex: 0 0 120px;
      align-items: center;
      justify-content: center;
      height: 120px;
      border-radius: 50%;
      background: conic-gradient(
        #3b82f6 0deg 130deg,
        #10b981 130deg 223deg,
        #f59e0b 223deg 288deg,
        #ef4444 288deg 320deg,
        #8b5cf6 320deg 360deg
      );

      &::before {
        content: '';
        position: absolute;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
      }

      .pie-center {
        position: relative;
        z-index: 1;
        text-align: center;

        .pie-value {
          color: #333;
          font-size: 16px;
          font-weight: bold;
        }

        .pie-label {
          color: #666;
          font-size: 12px;
        }
      }
    }

    .pie-legend {
      flex: 1;

      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;

        .legend-color {
          width: 12px;
          height: 12px;
          margin-right: 8px;
          border-radius: 2px;
        }

        .legend-name {
          flex: 1;
          color: #333;
        }

        .legend-percent {
          margin-right: 8px;
          color: #666;
        }

        .legend-value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  .line-chart {
    .chart-placeholder {
      height: 200px;
      padding: 20px 0;

      .chart-label {
        fill: #666;
        font-size: 12px;
      }

      .chart-value {
        fill: #333;
        font-size: 10px;
      }
    }
  }

  .contract-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    .contract-item {
      padding: 15px;
      border-radius: 8px;
      background: #f8f9fa;
      text-align: center;

      .contract-label {
        margin-bottom: 8px;
        color: #666;
        font-size: 14px;
      }

      .contract-value {
        color: #333;
        font-size: 24px;
        font-weight: bold;
      }
    }
  }

  .contract-progress {
    .progress-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      span {
        flex: 0 0 80px;
        color: #666;
        font-size: 14px;
      }

      .progress-bar {
        flex: 1;
        height: 8px;
        margin-left: 15px;
        border-radius: 4px;
        background: #f0f0f0;

        .progress-fill {
          height: 100%;
          border-radius: 4px;
          background: linear-gradient(90deg, #1890ff, #69c0ff);
        }
      }
    }
  }

  .age-chart {
    .pie-chart-small {
      .pie-legend-right {
        .legend-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          padding: 8px 0;
          font-size: 14px;

          .legend-dot {
            width: 8px;
            height: 8px;
            margin-right: 12px;
            border-radius: 50%;

            &.blue {
              background: #1890ff;
            }

            &.green {
              background: #52c41a;
            }

            &.orange {
              background: #fa8c16;
            }

            &.red {
              background: #ff4d4f;
            }
          }

          span:nth-child(2) {
            flex: 1;
            color: #333;
          }

          span:nth-child(3) {
            color: #666;
            font-weight: 500;
          }
        }
      }
    }
  }

  .status-chart {
    text-align: center;

    .status-center {
      display: inline-block;
      margin-bottom: 20px;
      padding: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      .status-value {
        margin-bottom: 5px;
        font-size: 32px;
        font-weight: bold;
      }

      .status-label {
        opacity: 0.9;
        font-size: 14px;
      }
    }

    .status-items {
      .status-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        .status-label {
          color: #666;
          font-size: 14px;
        }

        .status-percent {
          color: #333;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }

  .payment-chart {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px 0;

    .payment-item {
      text-align: center;

      .payment-circle {
        display: flex;
        position: relative;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        margin: 0 auto 10px;
        border-radius: 50%;
        background: conic-gradient(#1890ff 0deg 255deg, #f0f0f0 255deg 360deg);

        &::before {
          content: '';
          position: absolute;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: white;
        }

        .payment-percent {
          position: relative;
          z-index: 1;
          color: #333;
          font-size: 16px;
          font-weight: bold;
        }
      }

      .payment-label {
        color: #666;
        font-size: 12px;
      }

      &:nth-child(2) .payment-circle {
        background: conic-gradient(#52c41a 0deg 112deg, #f0f0f0 112deg 360deg);
      }

      &:nth-child(3) .payment-circle {
        background: conic-gradient(#fa8c16 0deg 61deg, #f0f0f0 61deg 360deg);
      }
    }
  }
</style>
