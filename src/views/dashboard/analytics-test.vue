<template>
  <div class="test-page">
    <div class="test-header">
      <h1>数据分析仪表板</h1>
      <p>陕西省机关事务数字化管理服务监管平台</p>
    </div>

    <!-- 直接引入分析仪表板组件 -->
    <Analytics />
  </div>
</template>

<script lang="ts" setup>
  import Analytics from './analytics.vue';
</script>

<style lang="less" scoped>
  .test-page {
    min-height: 100vh;
    background: #f0f2f5;
  }

  .test-header {
    padding: 20px;
    border-bottom: 1px solid #e8e8e8;
    background: white;
    text-align: center;

    h1 {
      margin: 0 0 10px;
      color: #333;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
</style>
