<template>
  <div class="dashboard-container">
    <!-- 上半部分：左上和右上 -->
    <div class="upper-section">
      <!-- 左上区域：欢迎 + 卡片 + 图表 -->
      <div class="left-upper">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>WELCOME!</h1>
              <p class="subtitle">早安，陕西省机关事务数字化管理服务保障平台</p>
              <div class="department-info">
                <span class="tag">系统管理员</span>
                <span class="tag">陕西省机关事务管理服务中心</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-cards">
          <div class="stats-card blue">
            <div class="stats-content">
              <div class="stats-label">房屋数量（套）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+0.7%</span>
              </div>
            </div>
          </div>

          <div class="stats-card orange">
            <div class="stats-content">
              <div class="stats-label">车位数量（个）</div>
              <div class="stats-value">108</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value negative">-8%</span>
              </div>
            </div>
          </div>

          <div class="stats-card green">
            <div class="stats-content">
              <div class="stats-label">营业收入（万元）</div>
              <div class="stats-value">129.08</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+60%</span>
              </div>
            </div>
          </div>

          <div class="stats-card purple">
            <div class="stats-content">
              <div class="stats-label">月租收入（万元）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value positive">+60%</span>
              </div>
            </div>
          </div>

          <div class="stats-card red">
            <div class="stats-content">
              <div class="stats-label">欠费（万元）</div>
              <div class="stats-value">2,130</div>
              <div class="stats-change">
                <span class="change-text">同比上月</span>
                <span class="change-value negative">-0%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <div class="chart-card">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >空置率排名</h3
            >
            <VChart :option="vacancyRateOption" style="width: 100%; height: 100%" />
          </div>

          <div class="payment-section">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >合同情况</h3
            >
            <div class="payment-content">
              <div class="payment-item">
                <div class="payment-label">已到期</div>
                <div class="payment-count">235</div>
              </div>
              <div class="payment-item">
                <div class="payment-label">已到期</div> <div class="payment-count">235</div></div
              >
              <div class="payment-item">
                <div class="payment-label">已到期</div> <div class="payment-count">235</div></div
              >
              <div class="payment-item">
                <div class="payment-label">已到期</div> <div class="payment-count">235</div></div
              >
            </div>
            <div class="payment-status">
              <div class="status-item">
                <span class="status-label">已到期</span>
                <div class="progress-bar">
                  <div class="progress orange" style="width: 90%"></div>
                </div>
                <!-- <span class="status-count">0 50 100 150 200 250 300</span> -->
              </div>
              <div class="status-item">
                <span class="status-label">一月到期</span>
                <div class="progress-bar">
                  <div class="progress orange" style="width: 70%"></div>
                </div>
              </div>
              <div class="status-item">
                <span class="status-label">二月到期</span>
                <div class="progress-bar">
                  <div class="progress orange" style="width: 85%"></div>
                </div>
              </div>
              <div class="status-item">
                <span class="status-label">三月到期</span>
                <div class="progress-bar">
                  <div class="progress orange" style="width: 30%"></div>
                </div>
              </div>
              <div class="status-item">
                <span class="status-label"></span>
                <div class="flex items-center justify-between w-full" style="padding: 0 10px; padding-left: 25px">
                  <div>0</div>
                  <div>30</div>
                  <div>60</div>
                  <div>90</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右上区域：指南 + 常用功能 + 未缴费情况 -->
      <div class="right-upper">
        <!-- 常用功能区域 -->
        <div class="functions-section">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋年限</h3
          >
          <div style="height: 100%; margin-top: 1%">
            <VChart :option="houseAgeOption" style="width: 100%; height: 100%" />
          </div>
        </div>
        <!-- 常用功能区域 -->
        <div class="functions-section">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋状态</h3
          >
          <div style="height: 100%; margin-top: 1%">
            <VChart :option="houseStatusOption" style="width: 100%; height: 100%" />
          </div>
        </div>

        <!-- 未缴费情况区域 -->
      </div>
    </div>

    <!-- 待办事项表格 -->
    <div class="down-section">
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >收款类型占比</h3
        >
        <div style="height: calc(100% - 40px); margin-top: 1%">
          <VChart :option="paymentTypeOption" style="width: 100%; height: 100%" />
        </div>
      </div>
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >小区收入统计（本月）</h3
        >
      </div>
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >支付方式占比</h3
        >
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, onMounted, nextTick } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { useRouter } from 'vue-router';
  import VChart from 'vue-echarts';
  export default {
    name: 'Dashboard',
    components: {
      VChart,
    },
    setup() {
      // 空置率排名数据（根据图片中的数据）
      const vacancyRateData = ref([
        { name: '西五路109号楼', value: 40 },
        { name: '西五路78号楼', value: 35 },
        { name: '西五路82号楼', value: 32 },
        { name: '军转干部55号楼', value: 28 },
        { name: '军转干部51号楼', value: 25 },
        { name: '军转干部43号楼', value: 22 },
        { name: '军转干部41号楼', value: 18 },
        { name: '西七路420号楼', value: 15 },
        { name: '西五路141号楼', value: 12 },
        { name: '西五路162号楼', value: 8 },
      ]);

      // 房屋状态环图数据
      const houseStatusData = ref([
        { name: '已出租', value: 1280, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
        { name: '空置', value: 520, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
        { name: '维修中', value: 180, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
        { name: '待出租', value: 150, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
      ]);

      const paymentTypeData = ref([
        { name: '银行转账', value: 1850, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
        { name: '现金收款', value: 680, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
        { name: '支付宝', value: 420, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
        { name: '微信支付', value: 380, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
        { name: '其他', value: 120, color: '#722ED1', transparentColor: 'rgba(114, 46, 209, 0.25)' },
      ]);
      const houseAgeData = ref([
        { name: '5年以下', value: 450, color: '#1890FF' },
        { name: '5-10年', value: 680, color: '#52C41A' },
        { name: '10-15年', value: 520, color: '#FA8C16' },
        { name: '15-20年', value: 380, color: '#F5222D' },
        { name: '20年以上', value: 100, color: '#722ED1' },
      ]);
      // 计算收款类型总数
      const paymentTypeTotal = paymentTypeData.value.reduce((sum, item) => sum + item.value, 0);

      const paymentTypeOption = ref({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          data: paymentTypeData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 30,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 40,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = paymentTypeData.value.find((d) => d.name === name);
            const total = paymentTypeData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(1);
            const valueText = item.value;
            const percentText = '￥' + percentage + '%';
            return `{name|${name}}{value|${valueText}}`;
          },
        },
        // title: {
        //   show: true,
        //   text: paymentTypeTotal.toString(),
        //   x: '20%',
        //   top: '40%',
        //   textStyle: {
        //     fontSize: 24,
        //     fontWeight: 'bold',
        //     color: '#333',
        //   },
        // },
        graphic: [
          {
            type: 'group',
            left: '30%',
            top: '50%',
            bounding: 'raw', // bounding 改为 raw，否则下面对 textVerticalAlign 与 textAlign 的配置不会生效
            children: [
              {
                type: 'text',
                style: {
                  text: paymentTypeTotal.toString(),
                  fontSize: 24,
                  // 垂直居中
                  textVerticalAlign: 'middle',
                  // 水平居中
                  textAlign: 'center',
                },
              },
            ],
          },
        ],
        series: [
          {
            name: '收款类型',
            type: 'pie',
            radius: ['50%', '80%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: paymentTypeData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
          },
        ],
      });
      const houseAgeOption = ref({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: houseAgeData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = houseAgeData.value.find((d) => d.name === name);
            const total = houseAgeData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(1);
            const valueText = item.value + '套';
            const percentText = '' + percentage + '%';
            return `{name|${name}}{value|${valueText}}`;
          },
        },
        series: [
          {
            name: '房屋年限',
            type: 'pie',
            radius: '50%',
            center: ['25%', '50%'],
            data: houseAgeData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        ],
      });

      const houseStatusOption = ref({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          data: houseStatusData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 50,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = houseStatusData.value.find((d) => d.name === name);
            const total = houseStatusData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(1);

            const valueText = item.value + '套';
            const percentText = '' + percentage + '%';

            return `{name|${name}}{percent|${percentText}}`;
          },
        },
        series: [
          {
            name: '房屋状态',
            type: 'pie',
            radius: ['45%', '55%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: houseStatusData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
          },
          {
            name: '房屋状态内环',
            type: 'pie',
            radius: ['35%', '45%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: houseStatusData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.transparentColor,
              },
            })),
          },
        ],
      });

      // 空置率排名柱状图配置
      const vacancyRateOption = ref({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            return params[0].name + '<br/>空置率: ' + params[0].value + '%';
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%', // 增加底部空间以容纳两行标签
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: vacancyRateData.value.map((item) => {
            // 将长标签分为两行显示
            const name = item.name;
            if (name.length > 6) {
              // 如果标签长度超过6个字符，则分行显示
              const midPoint = Math.ceil(name.length / 2);
              return name.substring(0, midPoint) + '\n' + name.substring(midPoint);
            }
            return name;
          }),
          axisLabel: {
            rotate: 0, // 不旋转，因为已经分行了
            fontSize: 10,
            color: '#666',
            interval: 0, // 显示所有标签
            lineHeight: 14, // 设置行高
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0',
            },
          },
        },
        yAxis: {
          type: 'value',
          name: '',
          nameTextStyle: {
            color: '#666',
            fontSize: 12,
          },
          axisLabel: {
            color: '#666',
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
        series: [
          {
            name: '空置率',
            type: 'bar',
            data: vacancyRateData.value.map((item) => item.value),
            itemStyle: {
              color: function (params) {
                // 根据数值大小设置不同颜色
                const colors = ['#2E75FF', '#4A90E2', '#6BA3F5', '#8BB6F8', '#ABC9FB'];
                return colors[Math.floor(params.dataIndex / 2)] || '#2E75FF';
              },
              borderRadius: [2, 2, 0, 0],
            },
            barWidth: 20, // 固定柱状图宽度为30像素
            barMaxWidth: 30, // 设置最大宽度，确保宽度固定
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 10,
              color: '#666',
            },
          },
        ],
      });

      // 初始化空置率排名图表

      return {
        paymentTypeOption,
        houseStatusOption,
        houseAgeOption,
        vacancyRateOption,
      };
    },
  };
</script>

<style scoped>
  .dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    overflow: auto;
    background: url('@/assets/work/BG.png');
    background-position: center;
    background-size: cover;
  }

  /* 上半部分布局 */
  .upper-section {
    display: grid;
    grid-template-columns: 3fr 1fr;
    flex-shrink: 0;
    margin-bottom: 20px;
    gap: 20px;
  }

  /* 左上区域 */
  .left-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 右上区域 */
  .right-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 欢迎区域 */
  .welcome-section {
    padding: 30px;
    border-radius: 12px;
    background: url('@/assets/work/welcome.png');
    background-position: center;
    background-size: cover;
    color: rgb(46 117 255 / 100%);
  }

  .welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
  }

  /* 指南区域 */
  .guide-section {
    margin-bottom: 0;
  }

  .guide-card {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(92deg, #2e75ff 0%, #2eafff 100%);
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    gap: 15px;
  }

  .guide-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    border-radius: 10px;
    background: url('@/assets/work/指南.png');
    color: white;
    font-size: 24px;
  }

  .guide-content h5 {
    margin: 0;
    color: white;
    font-size: 12px;
    font-weight: 500;
  }

  .welcome-text h1 {
    margin: 0 0 10px;
    background: linear-gradient(to bottom, #41aafb, #2a55fd); /* 从左到右的线性渐变 */
    background-clip: text; /* 将背景裁剪到文字 */
    -webkit-text-fill-color: transparent; /* 使文字透明以显示背景 */
    color: #fff;
    font-family: 'DIN Black';
    font-size: 36px;
    font-style: normal;
    font-weight: bold;
    font-weight: 400;
    line-height: 40px;
    text-align: left;
    text-transform: none;
  }

  .subtitle {
    margin: 0 0 15px;
    opacity: 0.9;
    color: #333;
    font-size: 16px;
  }

  .department-info {
    display: flex;
    gap: 10px;
  }

  .tag {
    padding: 4px 12px;
    border-radius: 20px;
    background: rgb(255 255 255 / 20%);
    color: #2e75ff;
    font-size: 14px;
  }

  .welcome-image {
    width: 200px;
    height: 120px;
  }

  .illustration {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120"><rect width="200" height="120" fill="%23ffffff20"/><circle cx="100" cy="60" r="30" fill="%23ffffff40"/></svg>');
    background-size: cover;
  }

  /* 统计卡片区域 */
  .stats-section {
    margin: 0;
  }

  .stats-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 10px;
  }

  .stat-card {
    display: flex;
    position: relative;
    align-items: center;
    padding: 20px;
    transition: transform 0.2s;
    border-radius: 12px;
    background: #f1f6ff;
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .stat-card.highlight {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin-right: 15px;
    border-radius: 10px;
  }

  .stat-icon.house {
    background: url('@/assets/work/可出租房屋.png');
  }

  .stat-icon.key {
    background: url('@/assets/work/已出售数量.png');
  }

  .stat-icon.vacancy {
    background: url('@/assets/work/空置率.png');
  }

  .stat-icon.revenue {
    background: url('@/assets/work/年度收益.png');
  }

  .stat-icon.monthly {
    background: url('@/assets/work/月度收益.png');
  }

  .stat-icon.public {
    background: url('@/assets/work/欠费.png');
  }

  .stat-icon.contract {
    background: url('@/assets/work/合同到期.png');
  }

  .stat-icon.period {
    background: url('@/assets/work/三个月到期.png');
  }

  .stat-content {
    flex: 1;
  }

  .stat-title {
    /* margin-bottom: 5px; */
    color: #333;
    font-size: 12px;
  }

  .stat-card.highlight .stat-title {
    color: rgb(255 255 255 / 80%);
  }

  .stat-number {
    color: rgb(46 117 255 / 100%);
    font-size: 28px;
    font-weight: bold;
  }

  .stat-number.large {
    font-size: 28px;
  }

  .stat-card.highlight .stat-number {
    color: white;
  }

  .unit {
    color: #333;
    font-size: 12px;
    font-weight: normal;
  }

  .stat-change {
    position: absolute;
    bottom: 0;
    left: 10%;
    width: 80%;
    font-size: 12px;
  }

  .stat-change.positive {
    color: #4caf50;
  }

  /* 图表容器 */
  .charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }

  .chart-card {
    height: 250px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .chart-card h3 {
    margin: 0 0 20px;
    color: #333;
    font-size: 16px;
  }

  .chart-content {
    width: 100%;
    height: calc(100% - 40px);
  }

  .donut-chart,
  .pie-chart {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: conic-gradient(
      #2196f3 0deg 137deg,
      #9c27b0 137deg 266deg,
      #ff9800 266deg 316deg,
      #9e9e9e 316deg 360deg
    );
  }

  .donut-chart::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
  }

  .chart-center {
    z-index: 1;
    text-align: center;
  }

  .total-number {
    color: #333;
    font-size: 18px;
    font-weight: bold;
  }

  .total-label {
    color: #666;
    font-size: 12px;
  }

  .chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 12px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 2px;
  }

  .legend-color.blue {
    background: #2196f3;
  }

  .legend-color.purple {
    background: #9c27b0;
  }

  .legend-color.orange {
    background: #ff9800;
  }

  .legend-color.gray {
    background: #9e9e9e;
  }

  .legend-color.red {
    background: #f44336;
  }

  .legend-color.yellow {
    background: #ffeb3b;
  }

  .legend-color.green {
    background: #4caf50;
  }

  /* 功能区域 */
  .functions-section {
    height: 304px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  /* 未缴费情况区域 */
  .payment-section {
    height: 250px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .functions-section h3 {
    margin: 0 0 15px;
    color: #333;
    font-size: 16px;
  }

  .function-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 30px;
  }

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    padding: 12px 8px;
    transition: all 0.2s;
    border-radius: 8px;
    cursor: pointer;
  }

  .function-item:hover {
    transform: translateY(-2px);
    background: #e3f2fd;
  }

  .function-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 45px;

    /* background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white; */
    font-size: 18px;
  }

  .function-icon.tenant {
    background: url('@/assets/work/常用功能1.png');
    background-size: 100% 100%;
  }

  .function-icon.contract-manage {
    background: url('@/assets/work/常用功能2.png');
    background-size: 100% 100%;
  }

  .function-icon.payment {
    background: url('@/assets/work/常用功能3.png');
    background-size: 100% 100%;
  }

  .function-icon.schedule {
    background: url('@/assets/work/常用功能4.png');
    background-size: 100% 100%;
  }

  .function-icon.report {
    background: url('@/assets/work/常用功能5.png');
    background-size: 100% 100%;
  }

  .function-icon.settings {
    background: url('@/assets/work/常用功能6.png');
    background-size: 100% 100%;
  }

  .function-item span {
    color: #666;
    font-size: 12px;
    text-align: center;
  }

  .payment-status {
    margin-top: 10px;
  }

  .payment-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding: 0 10px;
    border-radius: 10px;
    background: #f8f3ff;
    box-shadow: 0 1px 2px 0 rgb(46 117 255 / 10%);
    font-size: 12px;
  }

  .payment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }

  .payment-count {
    color: #fea740;
    font-size: 16px;
    font-weight: bold;
  }

  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
  }

  .status-label {
    width: 60px;
    color: #666;
  }

  .progress-bar {
    flex: 1;
    height: 8px;
    margin: 0 10px;
    overflow: hidden;
    border-radius: 3px;
    background: rgb(146 90 239 / 10%);
  }

  .progress {
    height: 100%;
    border-radius: 3px;
  }

  .progress.green {
    background: #4caf50;
  }

  .progress.orange {
    background: #925aef;
  }

  .status-count {
    color: #999;
    font-size: 10px;
  }

  /* 待办事项表格 */
  .down-section {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
  }

  .table-container {
    flex: 1;
    overflow: auto auto;
  }

  .todo-table {
    width: 100%;
    border-collapse: collapse;
  }

  .todo-table th,
  .todo-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    text-align: left;
  }

  .todo-table th {
    background: rgb(234 241 255 / 100%);
    color: #333;
    font-weight: 600;
  }

  .todo-table td {
    color: #666;
  }

  .todo-table tbody tr:hover {
    background: #f8f9fa;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    padding: clamp(10px, 1.5vw, 15px) 0;
    background: url('@/assets/work/中间数据背景.png');
    background-size: 100% 100%;
    gap: clamp(5px, 1vw, 10px);

    @media (min-width: 1200px) {
      grid-template-columns: repeat(5, 1fr);
      gap: 0;
    }

    .stats-card {
      min-width: 0; /* 防止内容溢出 */
      padding: clamp(10px, 1.5vw, 20px);

      .stats-icon {
        margin-bottom: 15px;
        font-size: clamp(20px, 2vw, 24px);
      }

      .stats-content {
        .stats-label {
          margin-bottom: 8px;
          color: #666;
          font-size: clamp(12px, 1.2vw, 14px);
          line-height: 1.3;
        }

        .stats-value {
          margin-bottom: 8px;
          font-size: clamp(20px, 2.5vw, 28px);
          font-weight: bold;
          line-height: 1.2;
        }

        .stats-change {
          display: flex;
          justify-content: space-between;
          font-size: clamp(10px, 1vw, 12px);
          gap: 5px;

          .change-text {
            flex-shrink: 0;
            color: #999;
          }

          .change-value {
            font-weight: bold;
            text-align: right;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }
          }
        }
      }

      &.blue {
        .stats-icon {
          color: #1890ff;
        }

        .stats-value {
          color: #1890ff;
        }
      }

      &.orange {
        .stats-icon {
          color: #fa8c16;
        }

        .stats-value {
          color: #fa8c16;
        }
      }

      &.green {
        .stats-icon {
          color: #52c41a;
        }

        .stats-value {
          color: #52c41a;
        }
      }

      &.purple {
        .stats-icon {
          color: #722ed1;
        }

        .stats-value {
          color: #722ed1;
        }
      }

      &.red {
        .stats-icon {
          color: #ff4d4f;
        }

        .stats-value {
          color: #ff4d4f;
        }
      }
    }
  }
</style>
