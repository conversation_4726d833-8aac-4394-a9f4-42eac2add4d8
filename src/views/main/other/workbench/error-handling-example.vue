<template>
  <div class="error-handling-example">
    <h2>错误处理示例</h2>
    
    <div class="status-section">
      <h3>API调用状态</h3>
      <div class="status-grid">
        <div class="status-item" :class="getStatusClass('numerical')">
          <span class="status-label">数值统计</span>
          <span class="status-indicator">{{ getStatusText('numerical') }}</span>
        </div>
        <div class="status-item" :class="getStatusClass('houseStatus')">
          <span class="status-label">房屋状态</span>
          <span class="status-indicator">{{ getStatusText('houseStatus') }}</span>
        </div>
        <div class="status-item" :class="getStatusClass('collectionType')">
          <span class="status-label">收款类别</span>
          <span class="status-indicator">{{ getStatusText('collectionType') }}</span>
        </div>
        <div class="status-item" :class="getStatusClass('notPayment')">
          <span class="status-label">未缴费统计</span>
          <span class="status-indicator">{{ getStatusText('notPayment') }}</span>
        </div>
      </div>
    </div>

    <div class="control-section">
      <button @click="loadAllData" :disabled="isLoading">
        {{ isLoading ? '加载中...' : '加载所有数据' }}
      </button>
      <button @click="simulateError" :disabled="isLoading">
        模拟部分API失败
      </button>
      <button @click="resetStatus">重置状态</button>
    </div>

    <div class="data-section">
      <h3>加载的数据</h3>
      <div class="data-grid">
        <div class="data-item">
          <h4>数值统计数据</h4>
          <pre>{{ JSON.stringify(numericalData, null, 2) }}</pre>
        </div>
        <div class="data-item">
          <h4>房屋状态数据</h4>
          <pre>{{ JSON.stringify(houseStatusData, null, 2) }}</pre>
        </div>
        <div class="data-item">
          <h4>收款类别数据</h4>
          <pre>{{ JSON.stringify(collectionTypeData, null, 2) }}</pre>
        </div>
        <div class="data-item">
          <h4>未缴费数据</h4>
          <pre>{{ JSON.stringify(notPaymentData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  getPermissionCollectionType,
  getPermissionHouseStatusStatistics,
  getPermissionNumericalDataStatistics,
  getNotPaymentMonthTime,
} from '@/api/main/other';

// 数据状态
const numericalData = ref(null);
const houseStatusData = ref(null);
const collectionTypeData = ref(null);
const notPaymentData = ref(null);

// 加载状态
const loadingStatus = ref({
  numerical: 'idle', // idle, loading, success, error
  houseStatus: 'idle',
  collectionType: 'idle',
  notPayment: 'idle',
});

const isLoading = computed(() => {
  return Object.values(loadingStatus.value).some(status => status === 'loading');
});

const getStatusClass = (key: string) => {
  const status = loadingStatus.value[key];
  return {
    'status-idle': status === 'idle',
    'status-loading': status === 'loading',
    'status-success': status === 'success',
    'status-error': status === 'error',
  };
};

const getStatusText = (key: string) => {
  const status = loadingStatus.value[key];
  const statusMap = {
    idle: '未开始',
    loading: '加载中',
    success: '成功',
    error: '失败',
  };
  return statusMap[status] || '未知';
};

// 独立的API调用函数
const loadNumericalData = async () => {
  loadingStatus.value.numerical = 'loading';
  try {
    const response = await getPermissionNumericalDataStatistics();
    numericalData.value = response;
    loadingStatus.value.numerical = 'success';
    console.log('数值统计数据加载成功:', response);
  } catch (error) {
    loadingStatus.value.numerical = 'error';
    console.error('数值统计数据加载失败:', error);
  }
};

const loadHouseStatusData = async () => {
  loadingStatus.value.houseStatus = 'loading';
  try {
    const response = await getPermissionHouseStatusStatistics();
    houseStatusData.value = response;
    loadingStatus.value.houseStatus = 'success';
    console.log('房屋状态数据加载成功:', response);
  } catch (error) {
    loadingStatus.value.houseStatus = 'error';
    console.error('房屋状态数据加载失败:', error);
  }
};

const loadCollectionTypeData = async () => {
  loadingStatus.value.collectionType = 'loading';
  try {
    const response = await getPermissionCollectionType();
    collectionTypeData.value = response;
    loadingStatus.value.collectionType = 'success';
    console.log('收款类别数据加载成功:', response);
  } catch (error) {
    loadingStatus.value.collectionType = 'error';
    console.error('收款类别数据加载失败:', error);
  }
};

const loadNotPaymentData = async () => {
  loadingStatus.value.notPayment = 'loading';
  try {
    const response = await getNotPaymentMonthTime();
    notPaymentData.value = response;
    loadingStatus.value.notPayment = 'success';
    console.log('未缴费数据加载成功:', response);
  } catch (error) {
    loadingStatus.value.notPayment = 'error';
    console.error('未缴费数据加载失败:', error);
  }
};

// 加载所有数据
const loadAllData = async () => {
  console.log('开始加载所有数据...');
  
  // 并行执行所有API调用，各自独立处理错误
  await Promise.all([
    loadNumericalData(),
    loadHouseStatusData(),
    loadCollectionTypeData(),
    loadNotPaymentData(),
  ]);
  
  console.log('所有API调用完成');
};

// 模拟部分API失败
const simulateError = async () => {
  console.log('模拟部分API失败...');
  
  // 模拟成功的API
  await loadNumericalData();
  await loadHouseStatusData();
  
  // 模拟失败的API
  loadingStatus.value.collectionType = 'loading';
  setTimeout(() => {
    loadingStatus.value.collectionType = 'error';
    console.error('模拟收款类别API失败');
  }, 1000);
  
  loadingStatus.value.notPayment = 'loading';
  setTimeout(() => {
    loadingStatus.value.notPayment = 'error';
    console.error('模拟未缴费API失败');
  }, 1500);
};

// 重置状态
const resetStatus = () => {
  Object.keys(loadingStatus.value).forEach(key => {
    loadingStatus.value[key] = 'idle';
  });
  numericalData.value = null;
  houseStatusData.value = null;
  collectionTypeData.value = null;
  notPaymentData.value = null;
};
</script>

<style scoped>
.error-handling-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-section {
  margin-bottom: 30px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  border: 2px solid;
  transition: all 0.3s ease;
}

.status-idle {
  border-color: #ddd;
  background: #f9f9f9;
}

.status-loading {
  border-color: #2e75ff;
  background: #e3f2fd;
  animation: pulse 1.5s infinite;
}

.status-success {
  border-color: #4caf50;
  background: #e8f5e8;
}

.status-error {
  border-color: #f44336;
  background: #ffebee;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-label {
  font-weight: 500;
}

.status-indicator {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
}

.control-section {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.control-section button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #2e75ff;
  color: white;
  cursor: pointer;
  transition: background 0.2s;
}

.control-section button:hover:not(:disabled) {
  background: #1e5bdf;
}

.control-section button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.data-section {
  margin-top: 30px;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.data-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: white;
}

.data-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.data-item pre {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}
</style>
