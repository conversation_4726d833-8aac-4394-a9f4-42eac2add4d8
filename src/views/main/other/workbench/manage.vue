<template>
  <div class="dashboard-container">
    <!-- 上半部分：左上和右上 -->
    <div class="upper-section">
      <!-- 左上区域：欢迎 + 卡片 + 图表 -->
      <div class="left-upper">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>WELCOME!</h1>
              <p class="subtitle">早安，陕西省机关事务数字化管理服务保障平台</p>
              <div class="department-info">
                <span class="tag">系统管理员</span>
                <span class="tag">陕西省机关事务管理服务中心</span>
                <button
                  @click="loadWorkbenchData"
                  style="
                    margin-left: 10px;
                    padding: 4px 8px;
                    border: none;
                    border-radius: 4px;
                    background: #2e75ff;
                    color: white;
                    cursor: pointer;
                  "
                  >刷新数据</button
                >
              </div>
            </div>
            <!-- <div class="welcome-image">
              <div class="illustration"></div>
            </div> -->
          </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-section">
          <div class="stats-row">
            <div class="stat-card">
              <div class="stat-icon house"></div>
              <div class="stat-content">
                <div class="stat-title">可出租房屋</div>
                <div class="stat-number">{{ numericalData.rentableHouses }} <span class="unit">套</span></div>
                <div class="stat-change positive">
                  <div class="status-item">
                    <div class="progress-bar" style="height: 6px; color: #2e75ff">
                      <div class="progress green" style="width: 58.8%; background-color: #2e75ff; color: #2e75ff"></div>
                    </div>
                    <span style="color: #2e75ff">已租{{ numericalData.rentedHouses }}/</span
                    ><span style="color: #333">空置{{ numericalData.vacantHouses }}</span>
                  </div></div
                >
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon key"></div>
              <div class="stat-content">
                <div class="stat-title">已出售数量</div>
                <div class="stat-number">{{ numericalData.soldHouses }} <span class="unit">套</span></div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon vacancy"></div>
              <div class="stat-content">
                <div class="stat-title">空置率</div>
                <div class="stat-number">{{ numericalData.vacancyRate }} <span class="unit">%</span></div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon revenue"></div>
              <div class="stat-content">
                <div class="stat-title">年度收益</div>
                <div class="stat-number">{{ numericalData.annualRevenue }} <span class="unit">万元</span></div>
              </div>
            </div>
          </div>

          <div class="stats-row">
            <div class="stat-card">
              <div class="stat-icon monthly"></div>
              <div class="stat-content">
                <div class="stat-title">月度收益</div>
                <div class="stat-number">{{ numericalData.monthlyRevenue }} <span class="unit">万元</span></div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon public"></div>
              <div class="stat-content">
                <div class="stat-title">欠费</div>
                <div class="stat-number">{{ numericalData.arrears }} <span class="unit">万元</span></div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon contract"></div>
              <div class="stat-content">
                <div class="stat-title">合同到期</div>
                <div class="stat-number">{{ numericalData.contractExpiry }}<span class="unit">套</span></div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon period"></div>
              <div class="stat-content">
                <div class="stat-title">三个月到期</div>
                <div class="stat-number">{{ numericalData.threeMonthExpiry }} <span class="unit">套</span></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <div class="chart-card">
            <h3 class="flex items-center" @click="() => handleRoute(7)">
              <div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >房屋状态</h3
            >
            <div ref="houseStatusChart" class="chart-content"></div>
          </div>

          <div class="chart-card">
            <h3 class="flex items-center" @click="() => handleRoute(8)"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >收款类别占比</h3
            >
            <div ref="paymentTypeChart" class="chart-content"></div>
          </div>
        </div>
      </div>

      <!-- 右上区域：指南 + 常用功能 + 未缴费情况 -->
      <div class="right-upper">
        <!-- 指南区域 -->
        <div class="guide-section">
          <div class="guide-card">
            <div class="guide-icon"></div>
            <div class="guide-content">
              <h5
                >不确定从哪里开始，查看
                <span style="text-decoration: underline; cursor: pointer" @click="handleZhiNan">使用指南</span>。</h5
              >
            </div>
          </div>
        </div>

        <!-- 常用功能区域 -->
        <div class="functions-section">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >常用功能</h3
          >
          <div class="function-grid">
            <div class="function-item" @click="() => handleRoute(1)">
              <div class="function-icon tenant"></div>
              <span>管理中心</span>
            </div>
            <div class="function-item" @click="() => handleRoute(2)">
              <div class="function-icon contract-manage"></div>
              <span>合同结算</span>
            </div>
            <div class="function-item" @click="() => handleRoute(3)">
              <div class="function-icon payment"></div>
              <span>我的缴费单</span>
            </div>
            <div class="function-item" @click="() => handleRoute(4)">
              <div class="function-icon schedule"></div>
              <span style="white-space: nowrap">日月收费统计</span>
            </div>
            <div class="function-item" @click="() => handleRoute(5)">
              <div class="function-icon report"></div>
              <span style="white-space: nowrap">临期合同管理</span>
            </div>
            <div class="function-item" @click="() => handleRoute(6)">
              <div class="function-icon settings"></div>
              <span style="white-space: nowrap">到期合同操作</span>
            </div>
          </div>
        </div>

        <!-- 未缴费情况区域 -->
        <div class="payment-section">
          <h3 class="flex items-center" @click="() => handleRoute(9)"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >未缴费情况</h3
          >
          <div class="payment-status">
            <div v-for="(item, index) in unpaidData" :key="index" class="status-item">
              <span class="status-label">{{ item.label }}</span>
              <div class="progress-bar">
                <div
                  class="progress"
                  :class="item.color"
                  :style="{ width: (item.value / item.maxValue) * 100 + '%' }"
                ></div>
                <span class="progress-text">{{ item.value }}户</span>
              </div>
              <!-- <span class="status-value">{{ item.value }}</span> -->
            </div>
            <div class="status-item">
              <span class="status-label"></span>
              <div class="flex items-center justify-between w-full" style="padding: 0 10px; padding-left: 25px">
                <div>0</div>
                <div>500</div>
                <div>1000</div>
                <div>2000</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 待办事项表格 -->
    <div class="todo-section">
      <h3 class="flex items-center" @click="() => handleRoute(10)"
        ><div
          style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
        ></div
        >待办事项列表</h3
      >
      <div class="table-container">
        <table class="todo-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>楼栋名称</th>
              <th>工作名称</th>
              <th>计划开始</th>
              <th>实际开始</th>
              <th>最新任务参与时间</th>
              <th>状态</th>
              <th>状态</th>
              <th>发起人</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody> </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, onMounted, nextTick } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { useRouter } from 'vue-router';
  import {
    getPermissionCollectionType,
    getPermissionHouseStatusStatistics,
    getPermissionNumericalDataStatistics,
    getNotPaymentMonthTime,
  } from '@/api/main/other';
  export default {
    name: 'Dashboard',
    setup() {
      const houseStatusChart = ref(null);
      const paymentTypeChart = ref(null);

      // 响应式数据
      const houseStatusData = ref([
        { name: '出售', value: 2590 },
        { name: '可出租', value: 1610 },
        { name: '自用', value: 0 },
        { name: '维修', value: 0 },
      ]);

      // 数值统计数据
      const numericalData = ref({
        rentableHouses: 1610, // 可出租房屋
        soldHouses: 2590, // 已出售数量
        vacancyRate: 41.2, // 空置率
        annualRevenue: 162.19, // 年度收益
        monthlyRevenue: 20.92, // 月度收益
        arrears: 0.19, // 欠费
        contractExpiry: 36, // 合同到期
        threeMonthExpiry: 77, // 三个月到期
        rentedHouses: 947, // 已租
        vacantHouses: 663, // 空置
      });
      const router = useRouter();
      const routeObj = {
        1: '/control/hire',
        2: '/control/settlement',
        3: '/jiaofei/myPayment',
        4: '/report/riyue',
        5: '/control/linqi',
        6: '/control/overLease',
        7: '/houseManage/courtyard',
        8: '/report/shishou',
        9: '/report/jiaofei',
        10: '/workorder/todo',
      };
      const handleRoute = (key) => {
        router.push(routeObj[key]);
      };

      // 收款类别数据
      const paymentTypeData = ref([
        { name: '房租', value: 44.55 },
        { name: '物业费', value: 45.47 },
        { name: '暖气费', value: 0.04 },
        { name: '水电费', value: 16.5 },
        { name: '押金', value: 51.07 },
        { name: '其他', value: 4.5 },
      ]);

      // 未缴费情况数据
      const unpaidData = ref([
        { label: '3个月以上', value: 2122, maxValue: 2500, color: 'green' },
        { label: '6个月以内', value: 860, maxValue: 2500, color: 'green' },
        { label: '9个月以上', value: 18, maxValue: 2500, color: 'green' },
        { label: '12个月以上', value: 15, maxValue: 2500, color: 'green' },
      ]);

      const { setOptions: setHouseStatusOptions } = useECharts(houseStatusChart);
      const { setOptions: setPaymentTypeOptions } = useECharts(paymentTypeChart);

      // API调用函数
      const loadWorkbenchData = async () => {
        try {
          console.log('开始加载工作台数据...');
          // 并行调用所有API
          const [numericalResponse, houseStatusResponse, collectionTypeResponse, notPaymentResponse] =
            await Promise.all([
              getPermissionNumericalDataStatistics(),
              getPermissionHouseStatusStatistics(),
              getPermissionCollectionType(),
              getNotPaymentMonthTime(),
            ]);

          console.log('API响应数据:', {
            numericalResponse,
            houseStatusResponse,
            collectionTypeResponse,
            notPaymentResponse,
          });

          // 更新数值统计数据
          if (numericalResponse) {
            const data = numericalResponse;
            // 根据API返回的数据结构更新numericalData
            if (Array.isArray(data)) {
              data.forEach((item) => {
                switch (item.key) {
                  case 'rentableHouses':
                    numericalData.value.rentableHouses = item.value;
                    break;
                  case 'soldHouses':
                    numericalData.value.soldHouses = item.value;
                    break;
                  case 'vacancyRate':
                    numericalData.value.vacancyRate = item.value;
                    break;
                  case 'annualRevenue':
                    numericalData.value.annualRevenue = item.value;
                    break;
                  case 'monthlyRevenue':
                    numericalData.value.monthlyRevenue = item.value;
                    break;
                  case 'arrears':
                    numericalData.value.arrears = item.value;
                    break;
                  case 'contractExpiry':
                    numericalData.value.contractExpiry = item.value;
                    break;
                  case 'threeMonthExpiry':
                    numericalData.value.threeMonthExpiry = item.value;
                    break;
                  case 'rentedHouses':
                    numericalData.value.rentedHouses = item.value;
                    break;
                  case 'vacantHouses':
                    numericalData.value.vacantHouses = item.value;
                    break;
                }
              });
            }
          }

          // 更新房屋状态数据
          if (houseStatusResponse && Array.isArray(houseStatusResponse)) {
            houseStatusData.value = houseStatusResponse.map((item) => ({
              name: item.name,
              value: item.value,
            }));
          }

          // 更新收款类别数据
          if (collectionTypeResponse && typeof collectionTypeResponse === 'object') {
            const data = collectionTypeResponse;
            paymentTypeData.value = Object.keys(data).map((key) => ({
              name: key,
              value: parseFloat(data[key]) || 0,
            }));
          }

          // 更新未缴费数据
          if (notPaymentResponse && Array.isArray(notPaymentResponse)) {
            unpaidData.value = notPaymentResponse.map((item) => ({
              label: item.keyView || item.key,
              value: item.value,
              maxValue: 2500, // 可以根据实际需求调整
              color: 'green',
            }));
          }

          // 重新初始化图表
          nextTick(() => {
            initHouseStatusChart();
            initPaymentTypeChart();
          });
        } catch (error) {
          console.error('加载工作台数据失败:', error);
        }
      };

      // 初始化房屋状态图表
      const initHouseStatusChart = () => {
        const colors = ['#2e75ff', '#00d4aa', '#ff6b6b', '#9c27b0', '#ff9800', '#4caf50'];
        const total = houseStatusData.value.reduce((sum, item) => sum + item.value, 0);

        // 外环数据
        const outerData = houseStatusData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index],
          },
        }));

        // 内环数据（透明度版本）
        const innerData = houseStatusData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index] + '40', // 添加透明度
          },
        }));

        setHouseStatusOptions({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: '15%',
            top: 'center',
            // itemGap: 15,
            itemWidth: 2, // 图标宽度变细
            itemHeight: 16, // 图标高度变细
            textStyle: {
              fontSize: 12,
              lineHeight: 20,
            },
            textStyle: {
              rich: {
                name: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
                value: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
                percent: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
              },
            },
            formatter: function (name) {
              const item = houseStatusData.value.find((item) => item.name === name);
              const percentage = ((item.value / total) * 100).toFixed(1);
              const paddedName = name.padEnd(4, '　'); // 名称固定4个字符宽度
              const paddedValue = item.value + '套'; // 数值固定5个字符宽度，左对齐
              const paddedPercentage = '' + percentage + '%'; // 百分比固定8个字符宽度，左对齐
              return `{name|${paddedName}}{percent|${paddedPercentage}}{value|${paddedValue}}`;
            },
            data: houseStatusData.value.map((item) => ({
              name: item.name,
              // icon: 'circle',
              textStyle: {
                color: '#333',
              },
            })),
          },
          color: colors,
          series: [
            {
              name: '房屋状态',
              type: 'pie',
              radius: ['80%', '100%'],
              center: ['20%', '50%'],
              data: outerData,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
            {
              name: '房屋状态',
              type: 'pie',
              radius: ['60%', '80%'],
              center: ['20%', '50%'],
              data: innerData,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              silent: true,
            },
          ],
        });
      };

      // 初始化收款类别图表
      const initPaymentTypeChart = () => {
        const colors = ['#2e75ff', '#00d4aa', '#ff9f43', '#ee5a52', '#9c27b0', '#ff9800', '#4caf50'];
        const total = paymentTypeData.value.reduce((sum, item) => sum + item.value, 0);

        // 外环数据
        const outerData = paymentTypeData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index],
          },
        }));

        // 内环数据（透明度版本）
        const innerData = paymentTypeData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index] + '40', // 添加透明度
          },
        }));

        setPaymentTypeOptions({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}% ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: '10%',
            top: 'center',
            itemGap: 6,
            itemWidth: 2, // 图标宽度变细
            itemHeight: 10, // 图标高度变细
            textStyle: {
              fontSize: 12,
            },
            textStyle: {
              rich: {
                name: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
                value: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
                percent: {
                  width: 70,
                  align: 'left',
                  fontSize: 14,
                },
              },
            },
            formatter: function (name) {
              const item = paymentTypeData.value.find((item) => item.name === name);
              const percentage = ((item.value / total) * 100).toFixed(1);
              const paddedName = name.padEnd(4, '　'); // 名称固定4个字符宽度
              const paddedValue = '¥ ' + item.value + '万元'; // 数值固定5个字符宽度，左对齐
              const paddedPercentage = '' + percentage + '%'; // 百分比固定8个字符宽度，左对齐
              return `{name|${paddedName}}{percent|${paddedPercentage}}{value|${paddedValue}}`;
            },
            data: paymentTypeData.value.map((item) => ({
              name: item.name,
              // icon: 'circle',
              textStyle: {
                color: '#333',
              },
            })),
          },
          color: colors,
          series: [
            {
              name: '收款类别',
              type: 'pie',
              radius: ['80%', '100%'],
              center: ['20%', '50%'],
              data: outerData,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
            {
              name: '收款类别',
              type: 'pie',
              radius: ['60%', '80%'],
              center: ['20%', '50%'],
              data: innerData,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              silent: true,
            },
          ],
        });
      };

      onMounted(() => {
        // 先初始化图表，然后加载数据
        nextTick(() => {
          initHouseStatusChart();
          initPaymentTypeChart();
          // 加载API数据
          loadWorkbenchData();
        });
      });
      const handleZhiNan = () => {
        window.open('http://zfgyzfxt.sxjgswgl.cn/fileproxy/base/2025/07/21/90594bb76cd048f99d71eb3455d48148.pdf');
      };
      return {
        houseStatusChart,
        paymentTypeChart,
        unpaidData,
        numericalData,
        handleZhiNan,
        handleRoute,
        loadWorkbenchData,
      };
    },
  };
</script>

<style scoped>
  .dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    overflow: auto;
    background: url('@/assets/work/BG.png');
    background-position: center;
    background-size: cover;
  }

  /* 上半部分布局 */
  .upper-section {
    display: grid;
    grid-template-columns: 3fr 1fr;
    flex-shrink: 0;
    margin-bottom: 20px;
    gap: 20px;
  }

  /* 左上区域 */
  .left-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 右上区域 */
  .right-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 欢迎区域 */
  .welcome-section {
    padding: 30px;
    border-radius: 12px;
    background: url('@/assets/work/welcome.png');
    background-position: center;
    background-size: cover;
    color: rgb(46 117 255 / 100%);
  }

  .welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
  }

  /* 指南区域 */
  .guide-section {
    margin-bottom: 0;
  }

  .guide-card {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(92deg, #2e75ff 0%, #2eafff 100%);
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    gap: 15px;
  }

  .guide-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    border-radius: 10px;
    background: url('@/assets/work/指南.png');
    color: white;
    font-size: 24px;
  }

  .guide-content h5 {
    margin: 0;
    color: white;
    font-size: 12px;
    font-weight: 500;
  }

  .welcome-text h1 {
    margin: 0 0 10px;
    background: linear-gradient(to bottom, #41aafb, #2a55fd); /* 从左到右的线性渐变 */
    background-clip: text; /* 将背景裁剪到文字 */
    -webkit-text-fill-color: transparent; /* 使文字透明以显示背景 */
    color: #fff;
    font-family: 'DIN Black';
    font-size: 36px;
    font-style: normal;
    font-weight: bold;
    font-weight: 400;
    line-height: 40px;
    text-align: left;
    text-transform: none;
  }

  .subtitle {
    margin: 0 0 15px;
    opacity: 0.9;
    color: #333;
    font-size: 16px;
  }

  .department-info {
    display: flex;
    gap: 10px;
  }

  .tag {
    padding: 4px 12px;
    border-radius: 20px;
    background: rgb(255 255 255 / 20%);
    color: #2e75ff;
    font-size: 14px;
  }

  .welcome-image {
    width: 200px;
    height: 120px;
  }

  .illustration {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120"><rect width="200" height="120" fill="%23ffffff20"/><circle cx="100" cy="60" r="30" fill="%23ffffff40"/></svg>');
    background-size: cover;
  }

  /* 统计卡片区域 */
  .stats-section {
    margin: 0;
  }

  .stats-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 10px;
  }

  .stat-card {
    display: flex;
    position: relative;
    align-items: center;
    padding: 20px;
    transition: transform 0.2s;
    border-radius: 12px;
    background: #f1f6ff;
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .stat-card.highlight {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin-right: 15px;
    border-radius: 10px;
  }

  .stat-icon.house {
    background: url('@/assets/work/可出租房屋.png');
  }

  .stat-icon.key {
    background: url('@/assets/work/已出售数量.png');
  }

  .stat-icon.vacancy {
    background: url('@/assets/work/空置率.png');
  }

  .stat-icon.revenue {
    background: url('@/assets/work/年度收益.png');
  }

  .stat-icon.monthly {
    background: url('@/assets/work/月度收益.png');
  }

  .stat-icon.public {
    background: url('@/assets/work/欠费.png');
  }

  .stat-icon.contract {
    background: url('@/assets/work/合同到期.png');
  }

  .stat-icon.period {
    background: url('@/assets/work/三个月到期.png');
  }

  .stat-content {
    flex: 1;
  }

  .stat-title {
    /* margin-bottom: 5px; */
    color: #333;
    font-size: 12px;
  }

  .stat-card.highlight .stat-title {
    color: rgb(255 255 255 / 80%);
  }

  .stat-number {
    color: rgb(46 117 255 / 100%);
    font-size: 28px;
    font-weight: bold;
  }

  .stat-number.large {
    font-size: 28px;
  }

  .stat-card.highlight .stat-number {
    color: white;
  }

  .unit {
    color: #333;
    font-size: 12px;
    font-weight: normal;
  }

  .stat-change {
    position: absolute;
    bottom: 0;
    left: 10%;
    width: 80%;
    font-size: 12px;
  }

  .stat-change.positive {
    color: #4caf50;
  }

  /* 图表容器 */
  .charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .chart-card {
    height: 230px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .chart-card h3 {
    margin: 0 0 20px;
    color: #333;
    font-size: 16px;
  }

  .chart-content {
    width: 100%;
    height: calc(100% - 40px);
  }

  .donut-chart,
  .pie-chart {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: conic-gradient(
      #2196f3 0deg 137deg,
      #9c27b0 137deg 266deg,
      #ff9800 266deg 316deg,
      #9e9e9e 316deg 360deg
    );
  }

  .donut-chart::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
  }

  .chart-center {
    z-index: 1;
    text-align: center;
  }

  .total-number {
    color: #333;
    font-size: 18px;
    font-weight: bold;
  }

  .total-label {
    color: #666;
    font-size: 12px;
  }

  .chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 12px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 2px;
  }

  .legend-color.blue {
    background: #2196f3;
  }

  .legend-color.purple {
    background: #9c27b0;
  }

  .legend-color.orange {
    background: #ff9800;
  }

  .legend-color.gray {
    background: #9e9e9e;
  }

  .legend-color.red {
    background: #f44336;
  }

  .legend-color.yellow {
    background: #ffeb3b;
  }

  .legend-color.green {
    background: #4caf50;
  }

  /* 功能区域 */
  .functions-section {
    padding: 14px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: #edf7ff;
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  /* 未缴费情况区域 */
  .payment-section {
    height: 280px;
    padding: 30px 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .functions-section h3 {
    margin: 0 0 15px;
    color: #333;
    font-size: 16px;
  }

  .function-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 30px;
  }

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    padding: 12px 8px;
    transition: all 0.2s;
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
  }

  .function-item:hover {
    transform: translateY(-2px);
    background: #e3f2fd;
  }

  .function-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 45px;

    /* background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white; */
    font-size: 18px;
  }

  .function-icon.tenant {
    background: url('@/assets/work/常用功能1.png');
    background-size: 100% 100%;
  }

  .function-icon.contract-manage {
    background: url('@/assets/work/常用功能2.png');
    background-size: 100% 100%;
  }

  .function-icon.payment {
    background: url('@/assets/work/常用功能3.png');
    background-size: 100% 100%;
  }

  .function-icon.schedule {
    background: url('@/assets/work/常用功能4.png');
    background-size: 100% 100%;
  }

  .function-icon.report {
    background: url('@/assets/work/常用功能5.png');
    background-size: 100% 100%;
  }

  .function-icon.settings {
    background: url('@/assets/work/常用功能6.png');
    background-size: 100% 100%;
  }

  .function-item span {
    color: #666;
    font-size: 12px;
    text-align: center;
  }

  .payment-status {
    margin-top: 40px;
  }

  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
  }

  .status-label {
    width: 70px;
    color: #666;
    font-size: 12px;
  }

  .progress-bar {
    position: relative;
    flex: 1;
    height: 8px;
    margin: 0 10px;
    overflow: visible;
    border-radius: 3px;
    background: #f0f0f0;
  }

  .progress {
    height: 100%;
    border-radius: 3px;
  }

  .progress.green {
    background: #4caf50;
  }

  .progress.orange {
    background: #ff9800;
  }

  .progress-text {
    position: absolute;
    top: -20px;
    right: 0;

    /* padding: 2px 4px; */

    /* transform: translateX(-50%); */
    border-radius: 2px;

    /* background: rgb(255 255 255 / 90%); */
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
    color: #333;
    font-size: 10px;
    font-weight: 500;
  }

  .status-value {
    min-width: 30px;
    color: #333;
    font-size: 12px;
    font-weight: 500;
    text-align: right;
  }

  .status-count {
    color: #999;
    font-size: 10px;
  }

  /* 待办事项表格 */
  .todo-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 200px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .todo-section h3 {
    margin: 0 0 20px;
    color: #333;
    font-size: 16px;
  }

  .table-container {
    flex: 1;
    overflow: auto auto;
  }

  .todo-table {
    width: 100%;
    border-collapse: collapse;
  }

  .todo-table th,
  .todo-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    text-align: left;
  }

  .todo-table th {
    background: rgb(234 241 255 / 100%);
    color: #333;
    font-weight: 600;
  }

  .todo-table td {
    color: #666;
  }

  .todo-table tbody tr:hover {
    background: #f8f9fa;
  }
</style>
