<template>
  <div class="api-test-container">
    <h2>工作台API测试</h2>
    
    <div class="test-section">
      <h3>API调用测试</h3>
      <div class="button-group">
        <button @click="testCollectionType" :disabled="loading">测试收款类别占比</button>
        <button @click="testHouseStatus" :disabled="loading">测试房屋状态统计</button>
        <button @click="testNumericalData" :disabled="loading">测试数值数据统计</button>
        <button @click="testNotPayment" :disabled="loading">测试未缴费统计</button>
        <button @click="testAllApis" :disabled="loading">测试所有API</button>
      </div>
      
      <div v-if="loading" class="loading">加载中...</div>
    </div>

    <div class="results-section">
      <h3>API响应结果</h3>
      <div class="result-item" v-for="(result, index) in results" :key="index">
        <h4>{{ result.title }}</h4>
        <div class="result-status" :class="result.success ? 'success' : 'error'">
          {{ result.success ? '成功' : '失败' }}
        </div>
        <pre class="result-data">{{ JSON.stringify(result.data, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  getPermissionCollectionType,
  getPermissionHouseStatusStatistics,
  getPermissionNumericalDataStatistics,
  getNotPaymentMonthTime,
} from '@/api/main/other';

const loading = ref(false);
const results = ref<Array<{
  title: string;
  success: boolean;
  data: any;
}>>([]);

const addResult = (title: string, success: boolean, data: any) => {
  results.value.unshift({
    title,
    success,
    data,
  });
  // 只保留最近10条结果
  if (results.value.length > 10) {
    results.value = results.value.slice(0, 10);
  }
};

const testCollectionType = async () => {
  loading.value = true;
  try {
    const response = await getPermissionCollectionType();
    addResult('收款类别占比统计', true, response);
  } catch (error) {
    addResult('收款类别占比统计', false, error);
  } finally {
    loading.value = false;
  }
};

const testHouseStatus = async () => {
  loading.value = true;
  try {
    const response = await getPermissionHouseStatusStatistics();
    addResult('房屋状态统计', true, response);
  } catch (error) {
    addResult('房屋状态统计', false, error);
  } finally {
    loading.value = false;
  }
};

const testNumericalData = async () => {
  loading.value = true;
  try {
    const response = await getPermissionNumericalDataStatistics();
    addResult('数值数据统计', true, response);
  } catch (error) {
    addResult('数值数据统计', false, error);
  } finally {
    loading.value = false;
  }
};

const testNotPayment = async () => {
  loading.value = true;
  try {
    const response = await getNotPaymentMonthTime();
    addResult('未缴费统计', true, response);
  } catch (error) {
    addResult('未缴费统计', false, error);
  } finally {
    loading.value = false;
  }
};

const testAllApis = async () => {
  loading.value = true;
  try {
    const [
      collectionResponse,
      houseStatusResponse,
      numericalResponse,
      notPaymentResponse,
    ] = await Promise.all([
      getPermissionCollectionType(),
      getPermissionHouseStatusStatistics(),
      getPermissionNumericalDataStatistics(),
      getNotPaymentMonthTime(),
    ]);

    addResult('所有API调用', true, {
      collectionType: collectionResponse,
      houseStatus: houseStatusResponse,
      numericalData: numericalResponse,
      notPayment: notPaymentResponse,
    });
  } catch (error) {
    addResult('所有API调用', false, error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.api-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.button-group button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #2e75ff;
  color: white;
  cursor: pointer;
  transition: background 0.2s;
}

.button-group button:hover:not(:disabled) {
  background: #1e5bdf;
}

.button-group button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
}

.results-section {
  margin-top: 30px;
}

.result-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.result-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.result-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 10px;
}

.result-status.success {
  background: #d4edda;
  color: #155724;
}

.result-status.error {
  background: #f8d7da;
  color: #721c24;
}

.result-data {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
}
</style>
