<template>
  <div class="dashboard-container">
    <!-- 上半部分：左上和右上 -->
    <div class="upper-section">
      <!-- 左上区域：欢迎 + 卡片 + 图表 -->
      <div class="left-upper">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>WELCOME!</h1>
              <p class="subtitle">早安，陕西省机关事务数字化管理服务保障平台</p>
              <div class="department-info">
                <span class="tag">系统管理员</span>
                <span class="tag">陕西省机关事务管理服务中心</span>
                <!-- <button
                  @click="loadAllData"
                  style="
                    margin-left: 10px;
                    padding: 4px 8px;
                    border: none;
                    border-radius: 4px;
                    background: #2e75ff;
                    color: white;
                    cursor: pointer;
                  "
                  >刷新数据</button
                > -->
              </div>
            </div>
          </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-cards">
          <div class="stats-card blue">
            <div class="stats-content">
              <div class="stats-label">可出租房屋（套）</div>
              <div class="stats-value">{{ numericalData.rentableHouses || 0 }}</div>
              <div class="stat-change2 positive2">
                <div class="status-item">
                  <div class="progress-bar" style="height: 6px; color: #2e75ff">
                    <div class="progress green" style="width: 58.8%; background-color: #2e75ff; color: #2e75ff"></div>
                  </div>
                  <span style="color: #2e75ff">已租{{ numericalData.rentedHouses || 0 }}/</span
                  ><span style="color: #333">空置{{ numericalData.vacantHouses || 0 }}</span>
                </div></div
              >
            </div>
          </div>

          <div class="stats-card orange">
            <div class="stats-content">
              <div class="stats-label">已出售房屋（套）</div>
              <div class="stats-value">{{ numericalData.soldHouses || 0 }}</div>
              <div class="stats-change">
                <span class="change-text">出售比例</span>
                <span class="change-value negative">{{ numericalData.saleRatio || 0 }}%</span>
              </div>
            </div>
          </div>

          <div class="stats-card green">
            <div class="stats-content">
              <div class="stats-label">年度收益（万元）</div>
              <div class="stats-value">{{ numericalData.annualRevenue || 0 }}</div>
              <div class="stats-change">
                <span class="change-text">同比去年</span>
                <span class="change-value positive">{{ numericalData.annualGrowth || 0 }}%</span>
              </div>
            </div>
          </div>

          <div class="stats-card purple">
            <div class="stats-content">
              <div class="stats-label">月租收益（万元）</div>
              <div class="stats-value">{{ numericalData.monthlyRevenue || 0 }}</div>
              <div class="stats-change">
                <span class="change-text">环比上月</span>
                <span class="change-value positive">{{ numericalData.monthlyGrowth || 0 }}%</span>
              </div>
            </div>
          </div>

          <div class="stats-card red">
            <div class="stats-content">
              <div class="stats-label">欠费（元）</div>
              <div class="stats-value">{{ numericalData.arrears || 0 }}</div>
              <div class="stats-change">
                <span class="change-text">环比上月</span>
                <span class="change-value negative">{{ numericalData.arrearsGrowth || 0 }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <div class="chart-card">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >空置率排名</h3
            >
            <VChart :option="vacancyRateOption" style="width: 100%; height: 100%" />
          </div>

          <div class="payment-section">
            <h3 class="flex items-center"
              ><div
                style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
              ></div
              >合同情况</h3
            >
            <div class="payment-content">
              <div class="payment-item">
                <div class="payment-label">签订中</div>
                <div class="payment-count">{{ contractSummaryData.signed }}</div>
              </div>
              <div class="payment-item">
                <div class="payment-label">合同变更中</div>
                <div class="payment-count">{{ contractSummaryData.executing }}</div>
              </div>
              <div class="payment-item">
                <div class="payment-label">结算中</div>
                <div class="payment-count">{{ contractSummaryData.settling }}</div>
              </div>
              <div class="payment-item">
                <div class="payment-label">已到期</div>
                <div class="payment-count">{{ contractSummaryData.expired }}</div>
              </div>
            </div>
            <div class="payment-status">
              <div v-for="(item, index) in contractData" :key="index" class="status-item">
                <span class="status-label">{{ item.label }}</span>
                <div class="progress-bar">
                  <div
                    class="progress"
                    :class="item.color"
                    :style="{ width: (item.value / progressMaxValue) * 100 + '%' }"
                  ></div>
                  <span class="progress-text">{{ item.value }}套</span>
                </div>
              </div>
              <div class="status-item">
                <span class="status-label"></span>
                <div class="flex items-center justify-between w-full" style="padding: 0 10px; padding-left: 25px">
                  <div v-for="scale in progressScale" :key="scale">{{ scale }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右上区域：指南 + 常用功能 + 未缴费情况 -->
      <div class="right-upper">
        <!-- 常用功能区域 -->
        <div class="functions-section">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋年限</h3
          >
          <div style="height: 100%; margin-top: 1%">
            <VChart :option="houseAgeOption" style="width: 100%; height: 100%" />
          </div>
        </div>
        <!-- 常用功能区域 -->
        <div class="functions-section">
          <h3 class="flex items-center"
            ><div
              style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
            ></div
            >房屋状态</h3
          >
          <div style="height: 100%; margin-top: 1%">
            <VChart :option="houseStatusOption" style="width: 100%; height: 100%" />
          </div>
        </div>

        <!-- 未缴费情况区域 -->
      </div>
    </div>

    <!-- 待办事项表格 -->
    <div class="down-section">
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >收款类别占比（万元）</h3
        >
        <div style="height: calc(100% - 40px); margin-top: 1%">
          <VChart :option="paymentTypeOption" style="width: 100%; height: 100%" />
        </div>
      </div>
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >小区收入统计（本月）</h3
        >
        <div style="height: calc(100% - 40px); margin-top: 1%">
          <VChart :option="communityIncomeOption" style="width: 100%; height: 100%" />
        </div>
      </div>
      <div class="chart-card" style="height: 270px">
        <h3 class="flex items-center"
          ><div
            style="width: 4px; height: 20px; margin-right: 10px; border-radius: 0 4px 4px 0; background: #2e75ff"
          ></div
          >支付方式占比</h3
        >
        <div class="payment-method-container">
          <div v-for="(item, index) in paymentMethodData" :key="index" class="payment-method-item">
            <div class="progress-ring-container">
              <VChart :option="getProgressRingOption(item)" style="width: 80px; height: 80px" />
            </div>
            <div class="payment-method-label">{{ item.name }}</div>
            <div class="payment-method-value">{{ formatCurrency(item.value) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, onMounted, computed } from 'vue';
  import VChart from 'vue-echarts';
  import {
    getNumericalDataStatistics,
    getStatisticsHousingYears,
    getVacancyRateRanking,
    getContractSituation,
    getExpiryStatistics,
    getHouseStatusStatistics,
    getCollectionTypeStatistics,
    getCommunityIncomeStatistics,
    getPaymentMethodStatistics,
  } from '@/api/main/other';
  export default {
    name: 'Dashboard',
    components: {
      VChart,
    },
    setup() {
      // 响应式数据
      const vacancyRateData = ref([]);
      const houseStatusData = ref([]);
      const paymentTypeData = ref([]);
      const houseAgeData = ref([]);
      const communityIncomeData = ref([]);
      const paymentMethodData = ref([]);
      const contractData = ref([]);
      const contractSummaryData = ref({
        signed: 0, // 已签订
        executing: 0, // 执行中
        settling: 0, // 待结算/结算中
        expired: 0, // 已到期
      });
      const numericalData = ref({
        rentableHouses: 0,
        rentedHouses: 0,
        vacantHouses: 0,
        soldHouses: 0,
        saleRatio: 0,
        annualRevenue: 0,
        annualGrowth: 0,
        monthlyRevenue: 0,
        monthlyGrowth: 0,
        arrears: 0,
        arrearsGrowth: 0,
      });

      // 计算属性
      const houseStatusTotal = computed(() => {
        if (!Array.isArray(houseStatusData.value) || houseStatusData.value.length === 0) {
          return 0;
        }
        return houseStatusData.value.reduce((sum, item) => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          return sum + value;
        }, 0);
      });

      const paymentTypeTotal = computed(() => {
        if (!Array.isArray(paymentTypeData.value) || paymentTypeData.value.length === 0) {
          return 0;
        }
        const total = paymentTypeData.value.reduce((sum, item) => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          return sum + value;
        }, 0);
        return parseFloat(total.toFixed(2));
      });

      // 动态计算进度条刻度
      const progressScale = computed(() => {
        if (!Array.isArray(contractData.value) || contractData.value.length === 0) {
          return [0, 30, 60, 90]; // 默认刻度
        }

        // 找到最大值
        const maxValue = Math.max(...contractData.value.map((item) => item.value));

        // 根据最大值动态生成合适的刻度
        if (maxValue <= 20) {
          return [0, 5, 10, 15, 20];
        } else if (maxValue <= 50) {
          return [0, 10, 25, 40, 50];
        } else if (maxValue <= 100) {
          return [0, 25, 50, 75, 100];
        } else if (maxValue <= 200) {
          return [0, 50, 100, 150, 200];
        } else {
          // 对于更大的值，使用动态计算
          const step = Math.ceil(maxValue / 4 / 10) * 10; // 向上取整到10的倍数
          return [0, step, step * 2, step * 3, step * 4];
        }
      });

      // 动态计算最大值
      const progressMaxValue = computed(() => {
        const scale = progressScale.value;
        return scale[scale.length - 1];
      });

      const paymentTypeOption = computed(() => ({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          data: paymentTypeData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = paymentTypeData.value.find((d) => d.name === name);
            const total = paymentTypeData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(2);
            const valueText = '￥' + item.value.toFixed(2) + '万';
            const percentText = '' + percentage + '%';
            return `{name|${name}}{value|${valueText}}{percent|${percentText}}`;
          },
        },
        // title: {
        //   show: true,
        //   text: paymentTypeTotal.toString(),
        //   x: '20%',
        //   top: '40%',
        //   textStyle: {
        //     fontSize: 24,
        //     fontWeight: 'bold',
        //     color: '#333',
        //   },
        // },
        graphic: [
          {
            type: 'group',
            left: '25%',
            top: '50%',
            bounding: 'raw', // bounding 改为 raw，否则下面对 textVerticalAlign 与 textAlign 的配置不会生效
            children: [
              {
                type: 'text',
                style: {
                  text: paymentTypeTotal.value.toString(),
                  fontSize: 24,
                  // 垂直居中
                  textVerticalAlign: 'middle',
                  // 水平居中
                  textAlign: 'center',
                },
              },
            ],
          },
        ],
        series: [
          {
            name: '收款类型',
            type: 'pie',
            radius: ['50%', '80%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: paymentTypeData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
          },
        ],
      }));
      const houseAgeOption = computed(() => ({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          data: houseAgeData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = houseAgeData.value.find((d) => d.name === name);
            const total = houseAgeData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(1);
            const valueText = item.value + '套';
            const percentText = '' + percentage + '%';
            return `{name|${name}}{value|${valueText}}{percent|${percentText}}`;
          },
        },
        series: [
          {
            name: '房屋年限',
            type: 'pie',
            radius: '55%',
            center: ['25%', '50%'],
            data: houseAgeData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        ],
      }));

      const houseStatusOption = computed(() => ({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          data: houseStatusData.value.map((item) => item.name),
          itemWidth: 2, // 图标宽度变细
          itemHeight: 16, // 图标高度变细
          textStyle: {
            rich: {
              name: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              value: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
              percent: {
                width: 60,
                align: 'left',
                fontSize: 12,
              },
            },
          },
          formatter: function (name) {
            const item = houseStatusData.value.find((d) => d.name === name);
            const total = houseStatusData.value.reduce((sum, d) => sum + d.value, 0);
            const percentage = ((item.value / total) * 100).toFixed(1);

            const valueText = item.value + '套';
            const percentText = '' + percentage + '%';

            return `{name|${name}}{value|${valueText}}{percent|${percentText}}`;
          },
        },
        graphic: [
          {
            type: 'group',
            left: '25%',
            top: '50%',
            bounding: 'raw',
            children: [
              {
                type: 'text',
                style: {
                  text: houseStatusTotal.value.toString(),
                  fontSize: 24,
                  fontWeight: 'bold',
                  textVerticalAlign: 'middle',
                  textAlign: 'center',
                  fill: '#333',
                },
              },
              {
                type: 'text',
                style: {
                  text: '套',
                  fontSize: 14,
                  textVerticalAlign: 'middle',
                  textAlign: 'center',
                  fill: '#666',
                  y: 20,
                },
              },
            ],
          },
        ],
        series: [
          {
            name: '房屋状态',
            type: 'pie',
            radius: ['45%', '55%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: houseStatusData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.color,
              },
            })),
          },
          {
            name: '房屋状态内环',
            type: 'pie',
            radius: ['35%', '45%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: houseStatusData.value.map((item) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: item.transparentColor,
              },
            })),
          },
        ],
      }));

      // 空置率排名柱状图配置
      const vacancyRateOption = computed(() => ({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            const dataIndex = params[0].dataIndex;
            const item = vacancyRateData.value[dataIndex];
            if (item) {
              return `${params[0].name}<br/>空置率: ${params[0].value}%<br/>总房间数: ${item.total || 0}套<br/>空置房间: ${item.kongzhi || 0}套`;
            }
            return params[0].name + '<br/>空置率: ' + params[0].value + '%';
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%', // 增加底部空间以容纳两行标签
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: vacancyRateData.value.map((item) => {
            // 将长标签分为两行显示
            const name = item.name;
            if (name.length > 6) {
              // 如果标签长度超过6个字符，则分行显示
              const midPoint = Math.ceil(name.length / 2);
              return name.substring(0, midPoint) + '\n' + name.substring(midPoint);
            }
            return name;
          }),
          axisLabel: {
            rotate: 0, // 不旋转，因为已经分行了
            fontSize: 10,
            color: '#666',
            interval: 0, // 显示所有标签
            lineHeight: 14, // 设置行高
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0',
            },
          },
        },
        yAxis: {
          type: 'value',
          name: '',
          nameTextStyle: {
            color: '#666',
            fontSize: 12,
          },
          axisLabel: {
            color: '#666',
            fontSize: 10,
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
        series: [
          {
            name: '空置率',
            type: 'bar',
            data: vacancyRateData.value.map((item) => item.value),
            itemStyle: {
              color: function (params) {
                // 根据数值大小设置不同颜色
                const colors = ['#2E75FF', '#4A90E2', '#6BA3F5', '#8BB6F8', '#ABC9FB'];
                return colors[Math.floor(params.dataIndex / 2)] || '#2E75FF';
              },
              borderRadius: [2, 2, 0, 0],
            },
            barWidth: 20, // 固定柱状图宽度为30像素
            barMaxWidth: 30, // 设置最大宽度，确保宽度固定
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 10,
              color: '#666',
            },
          },
        ],
      }));

      // 小区收入统计折线图配置
      const communityIncomeOption = computed(() => ({
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            const data = params[0];
            return `${data.name}<br/>月收入: ${data.value}万元`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333',
          },
        },
        grid: {
          left: '0%',
          right: '0%',
          top: '8%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: communityIncomeData.value.map((item) => item.name),
          axisLabel: {
            color: '#666',
            fontSize: 10,
            interval: 0,
            rotate: 0,
            formatter: function (value) {
              // 将长文本分成两行显示
              if (value.length > 4) {
                const mid = Math.ceil(value.length / 2);
                return value.substring(0, mid) + '\n' + value.substring(mid);
              }
              return value;
            },
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          name: '万元',
          nameTextStyle: {
            color: '#666',
            fontSize: 10,
          },
          axisLabel: {
            color: '#666',
            fontSize: 10,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'dashed',
            },
          },
        },
        series: [
          {
            name: '月收入',
            type: 'line',
            data: communityIncomeData.value.map((item) => item.value),
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              color: '#8B5CF6',
              width: 2,
            },
            itemStyle: {
              color: '#8B5CF6',
              borderColor: '#fff',
              borderWidth: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(139, 92, 246, 0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(139, 92, 246, 0.05)',
                  },
                ],
              },
            },
            markPoint: {
              data: [
                {
                  name: '南长巷22号',
                  value: '20.5万元',
                  xAxis: 3,
                  yAxis: 17,
                  itemStyle: {
                    color: 'transparent',
                  },
                  label: {
                    show: true,
                    formatter: '南长巷22号\n月收入 20.5万元',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    borderColor: '#8B5CF6',
                    borderWidth: 1,
                    borderRadius: 4,
                    padding: [4, 8],
                    fontSize: 10,
                    color: '#333',
                  },
                },
              ],
            },
          },
        ],
      }));

      // 生成进度环图表配置
      const getProgressRingOption = (item) => {
        // 计算支付方式总金额
        const total = paymentMethodData.value.reduce((sum, method) => sum + method.value, 0);
        // 计算当前项目的百分比
        const percentage = Math.round((item.value / total) * 100);

        return {
          series: [
            {
              type: 'pie',
              radius: ['60%', '80%'],
              center: ['50%', '50%'],
              startAngle: 90,
              data: [
                {
                  value: percentage,
                  itemStyle: {
                    color: item.color,
                  },
                  label: {
                    show: true,
                    position: 'center',
                    formatter: `${percentage}%`,
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: item.color,
                  },
                  labelLine: {
                    show: false,
                  },
                },
                {
                  value: 100 - percentage,
                  itemStyle: {
                    color: '#E8F4FD',
                  },
                  label: {
                    show: false,
                  },
                  labelLine: {
                    show: false,
                  },
                  silent: true,
                },
              ],
              silent: false,
            },
          ],
        };
      };

      // API 调用函数
      const loadNumericalData = async () => {
        try {
          const response = await getNumericalDataStatistics();
          console.log(response, 1111);

          if (response) {
            // 将键值对数组转换为对象
            const dataMap = {};
            response.forEach((item) => {
              dataMap[item.key] = parseFloat(item.value) || 0;
            });
            console.log(dataMap, 3333);

            numericalData.value = {
              rentableHouses: dataMap['可出租房屋'] || 0,
              rentedHouses: dataMap['已出租房屋'] || 0,
              vacantHouses: (dataMap['可出租房屋'] || 0) - (dataMap['已出租房屋'] || 0),
              soldHouses: dataMap['已出售房屋'] || 0,
              saleRatio:
                dataMap['已出售房屋'] && dataMap['总房间数']
                  ? ((dataMap['已出售房屋'] / dataMap['总房间数']) * 100).toFixed(2)
                  : 0,
              annualRevenue: (dataMap['年度收益'] / 10000).toFixed(2) || 0, // 转换为万元
              annualGrowth: dataMap['去年环比'] || 0,
              monthlyRevenue: (dataMap['月度收益'] / 10000).toFixed(2) || 0, // 转换为万元
              monthlyGrowth: dataMap['月度收益环比'] || 0,
              arrears: dataMap['本月欠款'] || 0,
              arrearsGrowth: dataMap['上月环比'] || 0,
            };
          }
        } catch (error) {
          console.error('获取数值数据失败:', error);
        }
      };

      const loadHousingYears = async () => {
        try {
          const response = await getStatisticsHousingYears();
          if (response) {
            // 假设返回格式类似 [{name: '15年以下', value: 1}, ...]
            houseAgeData.value = response.map((item, index) => ({
              name: item.name || item.key,
              value: parseFloat(item.value) || 0,
              color: getColorByIndex(index),
            }));
          }
        } catch (error) {
          console.error('获取房屋年限数据失败:', error);
          // 设置默认数据
          houseAgeData.value = [
            { name: '15年以下', value: 1, color: '#1890FF' },
            { name: '15-30年', value: 4, color: '#52C41A' },
            { name: '30-50年', value: 9, color: '#FA8C16' },
            { name: '50年以上', value: 9, color: '#722ED1' },
          ];
        }
      };

      const loadVacancyRate = async () => {
        try {
          const response = await getVacancyRateRanking();
          if (response && Array.isArray(response)) {
            // 按空置率降序排序，取前10个
            const sortedData = response
              .sort((a, b) => parseFloat(b.vacancyRate) - parseFloat(a.vacancyRate))
              .slice(0, 10);

            vacancyRateData.value = sortedData.map((item) => ({
              name: item.name,
              value: parseFloat(item.vacancyRate),
              total: item.total,
              kongzhi: item.kongzhi,
              id: item.id,
            }));
          }
        } catch (error) {
          console.error('获取空置率排名数据失败:', error);
          // 设置默认数据
          vacancyRateData.value = [
            { name: '省直机关东苑小区', value: 51.91 },
            { name: '东七道巷11号院', value: 37.5 },
            { name: '皇城东路51号院', value: 36.99 },
            { name: '药王洞141号院', value: 27.08 },
            { name: '皇城东路55号院', value: 16.67 },
            { name: '皇城东路41号院', value: 16.67 },
            { name: '西五路82付1号院', value: 15.86 },
            { name: '兴庆', value: 15.63 },
            { name: '莲湖路16号院', value: 14.63 },
            { name: '药王洞162号院', value: 14.17 },
          ];
        }
      };

      const loadContractData = async () => {
        try {
          const response = await getContractSituation();
          if (response && Array.isArray(response)) {
            // 处理合同情况数据，将键值对数组转换为合同统计数据
            const dataMap = {};
            response.forEach((item) => {
              dataMap[item.key] = parseFloat(item.value) || 0;
            });

            // 更新合同情况显示数据

            contractSummaryData.value = {
              signed: dataMap['签订中'] || 0, // 使用计算出的总合同数作为已签订
              executing: dataMap['合同变更中'] || 0,
              settling: dataMap['结算中'] || 0,
              expired: dataMap['已到期'] || 0,
            };

            console.log('合同情况数据:', dataMap, contractSummaryData.value);

            // 如果需要计算已签订数量，可以从其他数据源计算
            // 例如：已签订 = 总合同数 - 已到期 - 结算中 - 签订中 - 合同变更中
            // 这里可以根据实际业务逻辑调整
          }
        } catch (error) {
          console.error('获取合同情况数据失败:', error);
        }
      };

      const loadExpiryData = async () => {
        try {
          const response = await getExpiryStatistics();
          if (response && response.two && Array.isArray(response.two)) {
            // 使用 two 数组的数据处理临期数据
            contractData.value = response.two.map((item) => ({
              label: item.key,
              value: parseFloat(item.value) || 0,
              maxValue: progressMaxValue, // 使用动态计算的最大值
              color: 'orange',
            }));
          }
        } catch (error) {
          console.error('获取临期数据失败:', error);
          // 设置默认数据
          contractData.value = [
            { label: '已到期', value: 58, maxValue: 90, color: 'orange' },
            { label: '1月内到期', value: 12, maxValue: 90, color: 'orange' },
            { label: '2月内到期', value: 37, maxValue: 90, color: 'orange' },
            { label: '3月内到期', value: 52, maxValue: 90, color: 'orange' },
          ];
        }
      };

      const loadHouseStatus = async () => {
        try {
          const response = await getHouseStatusStatistics();
          if (response) {
            houseStatusData.value = response.map((item, index) => ({
              name: item.name || item.key,
              value: parseFloat(item.value) || 0,
              color: getColorByIndex(index),
              transparentColor: getTransparentColorByIndex(index),
            }));
          }
        } catch (error) {
          console.error('获取房屋状态数据失败:', error);
          // 设置默认数据
          houseStatusData.value = [
            { name: '已出售', value: 2590, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
            { name: '可出租', value: 1610, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
            { name: '维修中', value: 0, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
            { name: '自用', value: 0, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
          ];
        }
      };

      const loadCollectionType = async () => {
        try {
          const response = await getCollectionTypeStatistics();
          if (response) {
            // 将对象转换为数组格式
            paymentTypeData.value = Object.entries(response).map(([key, value], index) => ({
              name: key,
              value: parseFloat((parseFloat(value) / 10000).toFixed(2)), // 转换为万元并保留两位小数
              color: getColorByIndex(index),
              transparentColor: getTransparentColorByIndex(index),
            }));
          }
        } catch (error) {
          console.error('获取收款类别数据失败:', error);
          // 设置默认数据
          paymentTypeData.value = [
            { name: '房租', value: 44.55, color: '#1890FF', transparentColor: 'rgba(24, 144, 255, 0.25)' },
            { name: '物业费', value: 45.47, color: '#52C41A', transparentColor: 'rgba(82, 196, 26, 0.25)' },
            { name: '暖气费', value: 0.04, color: '#FA8C16', transparentColor: 'rgba(250, 140, 22, 0.25)' },
            { name: '水电费', value: 16.5, color: '#F5222D', transparentColor: 'rgba(245, 34, 45, 0.25)' },
            { name: '押金', value: 51.07, color: '#13C2C2', transparentColor: 'rgba(19, 194, 194, 0.25)' },
            { name: '其他', value: 4.5, color: '#722ED1', transparentColor: 'rgba(114, 46, 209, 0.25)' },
          ];
        }
      };

      const loadCommunityIncome = async () => {
        try {
          const response = await getCommunityIncomeStatistics();
          if (response) {
            communityIncomeData.value = response.map((item) => ({
              name: item.name || item.key,
              value: parseFloat(item.value) || 0,
            }));
          }
        } catch (error) {
          console.error('获取小区收入数据失败:', error);
          // 设置默认数据
          communityIncomeData.value = [
            { name: '兴庆', value: 55187.09 },
            { name: '省直机关东苑小区', value: 40867.91 },
            { name: '省直机关丰园小区', value: 11044.8 },
            { name: '南长巷22号院', value: 14723.95 },
            { name: '西号巷3号院', value: 5134.3 },
            { name: '药王洞162号院', value: 15316.32 },
            { name: '药王洞141号院', value: 9600 },
            { name: '西七路426号院', value: 11944 },
            { name: '皇城东路41号院', value: 8680 },
            { name: '皇城东路43号院', value: 30771.72 },
            { name: '皇城东路51号院', value: 3600 },
            { name: '西五路82付1号院', value: 1982.88 },
            { name: '西五路109号院', value: 425 },
          ];
        }
      };

      const loadPaymentMethod = async () => {
        try {
          const response = await getPaymentMethodStatistics();
          if (response) {
            paymentMethodData.value = response.map((item) => ({
              name: item.name || item.key,
              value: parseFloat(item.value) || 0,
              color: '#4A90E2',
            }));
          }
        } catch (error) {
          console.error('获取支付方式数据失败:', error);
          // 设置默认数据
          paymentMethodData.value = [
            { name: 'pos机支付', value: 851879.58, color: '#4A90E2' },
            { name: '线上支付', value: 574582.08, color: '#4A90E2' },
            { name: '现金支付', value: 163606.11, color: '#4A90E2' },
          ];
        }
      };

      // 辅助函数：根据索引获取颜色
      const getColorByIndex = (index) => {
        const colors = ['#1890FF', '#52C41A', '#FA8C16', '#F5222D', '#13C2C2', '#722ED1'];
        return colors[index % colors.length];
      };

      const getTransparentColorByIndex = (index) => {
        const colors = [
          'rgba(24, 144, 255, 0.25)',
          'rgba(82, 196, 26, 0.25)',
          'rgba(250, 140, 22, 0.25)',
          'rgba(245, 34, 45, 0.25)',
          'rgba(19, 194, 194, 0.25)',
          'rgba(114, 46, 209, 0.25)',
        ];
        return colors[index % colors.length];
      };

      // 加载所有数据
      const loadAllData = async () => {
        await Promise.all([
          loadNumericalData(),
          loadHousingYears(),
          loadVacancyRate(),
          loadContractData(),
          loadExpiryData(),
          loadHouseStatus(),
          loadCollectionType(),
          loadCommunityIncome(),
          loadPaymentMethod(),
        ]);
      };

      // 组件挂载时加载数据
      onMounted(() => {
        loadAllData();
      });

      // 格式化货币显示
      const formatCurrency = (value) => {
        if (value >= 10000) {
          return (value / 10000).toFixed(2) + '万元';
        }
        return value.toFixed(2) + '元';
      };

      return {
        // 图表配置
        paymentTypeOption,
        houseStatusOption,
        houseAgeOption,
        vacancyRateOption,
        communityIncomeOption,
        // 数据
        numericalData,
        vacancyRateData,
        houseStatusData,
        paymentTypeData,
        houseAgeData,
        communityIncomeData,
        paymentMethodData,
        contractData,
        contractSummaryData,
        // 计算属性
        houseStatusTotal,
        paymentTypeTotal,
        progressScale,
        progressMaxValue,
        // 函数
        getProgressRingOption,
        formatCurrency,
        loadAllData,
      };
    },
  };
</script>

<style scoped>
  .stat-change {
    position: absolute;
    bottom: 0;
    left: 10%;
    width: 80%;
    font-size: 12px;
  }

  .dashboard-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    overflow: auto;
    background: url('@/assets/work/BG.png');
    background-position: center;
    background-size: cover;
  }

  /* 上半部分布局 */
  .upper-section {
    display: grid;
    grid-template-columns: 3fr 1fr;
    flex-shrink: 0;
    margin-bottom: 20px;
    gap: 20px;
  }

  /* 左上区域 */
  .left-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 右上区域 */
  .right-upper {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* 欢迎区域 */
  .welcome-section {
    padding: 30px;
    border-radius: 12px;
    background: url('@/assets/work/welcome.png');
    background-position: center;
    background-size: cover;
    color: rgb(46 117 255 / 100%);
  }

  .welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
  }

  /* 指南区域 */
  .guide-section {
    margin-bottom: 0;
  }

  .guide-card {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 20px;
    border-radius: 12px;
    background: linear-gradient(92deg, #2e75ff 0%, #2eafff 100%);
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    gap: 15px;
  }

  .guide-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 26px;
    height: 26px;
    border-radius: 10px;
    background: url('@/assets/work/指南.png');
    color: white;
    font-size: 24px;
  }

  .guide-content h5 {
    margin: 0;
    color: white;
    font-size: 12px;
    font-weight: 500;
  }

  .welcome-text h1 {
    margin: 0 0 10px;
    background: linear-gradient(to bottom, #41aafb, #2a55fd); /* 从左到右的线性渐变 */
    background-clip: text; /* 将背景裁剪到文字 */
    -webkit-text-fill-color: transparent; /* 使文字透明以显示背景 */
    color: #fff;
    font-family: 'DIN Black';
    font-size: 36px;
    font-style: normal;
    font-weight: bold;
    font-weight: 400;
    line-height: 40px;
    text-align: left;
    text-transform: none;
  }

  .subtitle {
    margin: 0 0 15px;
    opacity: 0.9;
    color: #333;
    font-size: 16px;
  }

  .department-info {
    display: flex;
    gap: 10px;
  }

  .tag {
    padding: 4px 12px;
    border-radius: 20px;
    background: rgb(255 255 255 / 20%);
    color: #2e75ff;
    font-size: 14px;
  }

  .welcome-image {
    width: 200px;
    height: 120px;
  }

  .illustration {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120"><rect width="200" height="120" fill="%23ffffff20"/><circle cx="100" cy="60" r="30" fill="%23ffffff40"/></svg>');
    background-size: cover;
  }

  /* 统计卡片区域 */
  .stats-section {
    margin: 0;
  }

  .stats-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 10px;
  }

  .stat-card {
    display: flex;
    position: relative;
    align-items: center;
    padding: 20px;
    transition: transform 0.2s;
    border-radius: 12px;
    background: #f1f6ff;
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .stat-card.highlight {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    margin-right: 15px;
    border-radius: 10px;
  }

  .stat-icon.house {
    background: url('@/assets/work/可出租房屋.png');
  }

  .stat-icon.key {
    background: url('@/assets/work/已出售数量.png');
  }

  .stat-icon.vacancy {
    background: url('@/assets/work/空置率.png');
  }

  .stat-icon.revenue {
    background: url('@/assets/work/年度收益.png');
  }

  .stat-icon.monthly {
    background: url('@/assets/work/月度收益.png');
  }

  .stat-icon.public {
    background: url('@/assets/work/欠费.png');
  }

  .stat-icon.contract {
    background: url('@/assets/work/合同到期.png');
  }

  .stat-icon.period {
    background: url('@/assets/work/三个月到期.png');
  }

  .stat-content {
    flex: 1;
  }

  .stat-title {
    /* margin-bottom: 5px; */
    color: #333;
    font-size: 12px;
  }

  .stat-card.highlight .stat-title {
    color: rgb(255 255 255 / 80%);
  }

  .stat-number {
    color: rgb(46 117 255 / 100%);
    font-size: 28px;
    font-weight: bold;
  }

  .stat-number.large {
    font-size: 28px;
  }

  .stat-card.highlight .stat-number {
    color: white;
  }

  .unit {
    color: #333;
    font-size: 12px;
    font-weight: normal;
  }

  .stat-change2 {
    position: absolute;
    bottom: 10%;
    left: 6%;
    width: 80%;
    font-size: 12px;
  }

  .stat-change.positive {
    color: #4caf50;
  }

  /* 图表容器 */
  .charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }

  .chart-card {
    height: 250px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .chart-card h3 {
    margin: 0 0 20px;
    color: #333;
    font-size: 16px;
  }

  .chart-content {
    width: 100%;
    height: calc(100% - 40px);
  }

  .donut-chart,
  .pie-chart {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: conic-gradient(
      #2196f3 0deg 137deg,
      #9c27b0 137deg 266deg,
      #ff9800 266deg 316deg,
      #9e9e9e 316deg 360deg
    );
  }

  .donut-chart::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: white;
  }

  .chart-center {
    z-index: 1;
    text-align: center;
  }

  .total-number {
    color: #333;
    font-size: 18px;
    font-weight: bold;
  }

  .total-label {
    color: #666;
    font-size: 12px;
  }

  .chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 12px;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 2px;
  }

  .legend-color.blue {
    background: #2196f3;
  }

  .legend-color.purple {
    background: #9c27b0;
  }

  .legend-color.orange {
    background: #ff9800;
  }

  .legend-color.gray {
    background: #9e9e9e;
  }

  .legend-color.red {
    background: #f44336;
  }

  .legend-color.yellow {
    background: #ffeb3b;
  }

  .legend-color.green {
    background: #4caf50;
  }

  /* 功能区域 */
  .functions-section {
    height: 304px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  /* 未缴费情况区域 */
  .payment-section {
    height: 250px;
    padding: 20px;
    border: 3px solid #fff;
    border-radius: 20px;
    background: linear-gradient(180deg, #edf7ff 0%, #fff 100%);
    box-shadow: 0 2px 6px 0 rgb(46 117 255 / 20%);
  }

  .functions-section h3 {
    margin: 0 0 15px;
    color: #333;
    font-size: 16px;
  }

  .function-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 30px;
  }

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    padding: 12px 8px;
    transition: all 0.2s;
    border-radius: 8px;
    cursor: pointer;
  }

  .function-item:hover {
    transform: translateY(-2px);
    background: #e3f2fd;
  }

  .function-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 45px;

    /* background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white; */
    font-size: 18px;
  }

  .function-icon.tenant {
    background: url('@/assets/work/常用功能1.png');
    background-size: 100% 100%;
  }

  .function-icon.contract-manage {
    background: url('@/assets/work/常用功能2.png');
    background-size: 100% 100%;
  }

  .function-icon.payment {
    background: url('@/assets/work/常用功能3.png');
    background-size: 100% 100%;
  }

  .function-icon.schedule {
    background: url('@/assets/work/常用功能4.png');
    background-size: 100% 100%;
  }

  .function-icon.report {
    background: url('@/assets/work/常用功能5.png');
    background-size: 100% 100%;
  }

  .function-icon.settings {
    background: url('@/assets/work/常用功能6.png');
    background-size: 100% 100%;
  }

  .function-item span {
    color: #666;
    font-size: 12px;
    text-align: center;
  }

  .payment-status {
    margin-top: 10px;
  }

  .payment-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    min-height: 60px;

    /* padding: 12px 16px; */
    border: 1px solid rgb(46 117 255 / 10%);
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    box-shadow: 0 2px 8px 0 rgb(46 117 255 / 8%);
    font-size: 12px;
    gap: 8px;
  }

  .payment-item {
    display: flex;
    position: relative;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    transition: all 0.3s ease;
    border-radius: 8px;

    /* background: rgb(255 255 255 / 60%); */
  }

  .payment-label {
    margin-bottom: 4px;
    color: #666;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    text-align: center;
  }

  .payment-count {
    color: #2e75ff;
    font-size: 18px;
    font-weight: 600;
    line-height: 1;
  }

  /* 为不同状态添加不同的颜色 */
  .payment-item:nth-child(1) .payment-count {
    color: #52c41a; /* 签订中 - 绿色 */
  }

  .payment-item:nth-child(2) .payment-count {
    color: #1890ff; /* 合同变更中 - 蓝色 */
  }

  .payment-item:nth-child(3) .payment-count {
    color: #faad14; /* 结算中 - 橙色 */
  }

  .payment-item:nth-child(4) .payment-count {
    color: #f5222d; /* 已到期 - 红色 */
  }

  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
  }

  .status-label {
    width: 60px;
    color: #666;
  }

  .progress-bar {
    position: relative;
    flex: 1;
    height: 8px;
    margin: 0 10px;
    overflow: visible;
    border-radius: 3px;
    background: rgb(146 90 239 / 10%);
  }

  .progress {
    height: 100%;
    border-radius: 3px;
  }

  .progress.green {
    background: #4caf50;
  }

  .progress.orange {
    background: #925aef;
  }

  .progress-text {
    position: absolute;
    top: -15px;
    right: 0;
    border-radius: 2px;

    /* box-shadow: 0 1px 3px rgb(0 0 0 / 10%); */
    color: #333;
    font-size: 10px;
    font-weight: 500;
  }

  .status-count {
    color: #999;
    font-size: 10px;
  }

  /* 待办事项表格 */
  .down-section {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
  }

  .table-container {
    flex: 1;
    overflow: auto auto;
  }

  .todo-table {
    width: 100%;
    border-collapse: collapse;
  }

  .todo-table th,
  .todo-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    text-align: left;
  }

  .todo-table th {
    background: rgb(234 241 255 / 100%);
    color: #333;
    font-weight: 600;
  }

  .todo-table td {
    color: #666;
  }

  .todo-table tbody tr:hover {
    background: #f8f9fa;
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    padding: clamp(10px, 1.5vw, 15px) 0;
    background: url('@/assets/work/中间数据背景.png');
    background-size: 100% 100%;
    gap: clamp(5px, 1vw, 10px);

    @media (min-width: 1200px) {
      grid-template-columns: repeat(5, 1fr);
      gap: 0;
    }

    .stats-card {
      position: relative;
      min-width: 0; /* 防止内容溢出 */
      padding: clamp(10px, 1.5vw, 20px);

      .stats-icon {
        margin-bottom: 15px;
        font-size: clamp(20px, 2vw, 24px);
      }

      .stats-content {
        .stats-label {
          margin-bottom: 8px;
          color: #666;
          font-size: clamp(12px, 1.2vw, 14px);
          line-height: 1.3;
        }

        .stats-value {
          margin-bottom: 8px;
          font-size: clamp(20px, 2.5vw, 28px);
          font-weight: bold;
          line-height: 1.2;
        }

        .stats-change {
          display: flex;
          justify-content: space-between;
          font-size: clamp(10px, 1vw, 12px);
          gap: 5px;

          .change-text {
            flex-shrink: 0;
            color: #999;
          }

          .change-value {
            font-weight: bold;
            text-align: right;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }
          }
        }
      }

      &.blue {
        .stats-icon {
          color: #1890ff;
        }

        .stats-value {
          color: #1890ff;
        }
      }

      &.orange {
        .stats-icon {
          color: #fa8c16;
        }

        .stats-value {
          color: #fa8c16;
        }
      }

      &.green {
        .stats-icon {
          color: #52c41a;
        }

        .stats-value {
          color: #52c41a;
        }
      }

      &.purple {
        .stats-icon {
          color: #722ed1;
        }

        .stats-value {
          color: #722ed1;
        }
      }

      &.red {
        .stats-icon {
          color: #ff4d4f;
        }

        .stats-value {
          color: #ff4d4f;
        }
      }
    }
  }

  /* 支付方式占比样式 */
  .payment-method-container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: calc(100% - 60px);
    padding: 20px 0;
  }

  .payment-method-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .progress-ring-container {
    position: relative;
  }

  .payment-method-label {
    color: #666;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
  }

  .payment-method-value {
    margin-top: 2px;
    color: #333;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
  }
</style>
