import { BasicColumn, FormSchema } from '@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '流水号',
    dataIndex: 'id',
    width: 100,
  },
  {
    title: '房屋',
    dataIndex: 'houseAdress',
    width: 100,
  },
  {
    title: '住户',
    dataIndex: 'tenantName',
    width: 100,
  },
  {
    title: '缴费时间',
    dataIndex: 'paymentTime',
    width: 100,
  },
  {
    title: '计费期',
    dataIndex: 'startAndEnd',
    width: 100,
  },
  {
    title: '收费项目',
    dataIndex: 'typeView',
    width: 100,
  },
  {
    title: '应收',
    dataIndex: 'amountDue',
    width: 100,
  },
  {
    title: '本次缴费',
    dataIndex: 'paidAmount',
    width: 100,
  },
  {
    title: '欠费',
    dataIndex: 'arrearsAmount',
    width: 100,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: '[params.beginTime,params.endTime]',
    label: '查询时间',
    component: 'RangeDatePicker',
    defaultValue: [
      dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().endOf('month').format('YYYY-MM-DD 23:59:59'),
    ],
  },
];
