<template>
  <BasicTable @register="registerTable"> </BasicTable>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@/components/Table';
  import { columns, searchFormSchema } from './mingxi.data';
  import { AutoFormColProps } from '@/enums/appEnum';
  import { onMounted } from 'vue';
  import { getDailyChargeReport } from '@/api/gaoqian/report';
  import dayjs from 'dayjs';

  const [registerTable, { reload, setColumns }] = useTable({
    columns,
    api: getDailyChargeReport,
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,

    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      baseColProps: AutoFormColProps,
      autoSubmitOnEnter: true,
    },
  });
</script>

<style scoped></style>
