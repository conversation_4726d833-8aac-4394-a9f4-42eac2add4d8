import { BasicColumn, FormSchema } from '@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '操作员',
    dataIndex: 'operatorName',
    width: 100,
  },
  {
    title: '收取户数',
    dataIndex: 'householdCount',
    width: 100,
  },
  {
    title: '收缴金额',
    dataIndex: 'collectedAmount',
    width: 100,
  },
  {
    title: '冲抵金额',
    dataIndex: 'offsetAmount',
    width: 100,
  },
  {
    title: 'POS机',
    dataIndex: 'posAmount',
    width: 100,
  },
  {
    title: '现金',
    dataIndex: 'cashAmount',
    width: 100,
  },
  {
    title: '线上支付',
    dataIndex: 'onlinePaymentAmount',
    width: 100,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: '[params.beginTime,params.endTime]',
    label: '查询时间',
    component: 'RangeDatePicker',
    defaultValue: [
      dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
      dayjs().endOf('month').format('YYYY-MM-DD 23:59:59'),
    ],
  },
];
