import { BasicColumn, FormSchema } from '@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '',
    dataIndex: 'date',
    width: 100,
  },
  {
    title: '收费项目',
    dataIndex: 'type',
    width: 100,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'dateTime',
    label: '日期',
    component: 'DatePicker',
    // defaultValue: dayjs().startOf('day').format('YYYY-MM-DD'),
    componentProps: {
      style: { width: '100%' },
    },
  },
];
export const demoData = [
  {
    col1: '001-1-101',
    col2: '测试业主',
    col3: 76.57,
    col4: '物业费',
    col5: '2024-09-08 00:00:00',
    col6: '2024-09-09 15:18:04',
    col7: 22,
    col8: 0.73,
    col9: 229.71,
  },
  {
    col1: '002-1-102',
    col2: '欧阳',
    col3: 88.45,
    col4: '每月物业费',
    col5: '2024-08-01 00:00:00',
    col6: '2024-09-01 18:11:54',
    col7: 30,
    col8: 1.0,
    col9: 191.43,
  },
  {
    col1: '003-1-203',
    col2: '张三',
    col3: 65.42,
    col4: '停车费',
    col5: '2024-07-01 00:00:00',
    col6: '2024-07-15 10:11:54',
    col7: 15,
    col8: 0.5,
    col9: 95.71,
  },
  {
    col1: '004-1-304',
    col2: '李四',
    col3: 92.15,
    col4: '物业费',
    col5: '2024-05-01 00:00:00',
    col6: '2024-05-30 14:20:15',
    col7: 30,
    col8: 1.0,
    col9: 200.57,
  },
  {
    col1: '005-1-202',
    col2: '王五',
    col3: 70.89,
    col4: '每月物业费',
    col5: '2024-04-01 00:00:00',
    col6: '2024-04-20 09:30:50',
    col7: 20,
    col8: 0.67,
    col9: 150.78,
  },
  {
    col1: '006-1-105',
    col2: '欧阳',
    col3: 85.24,
    col4: '停车费',
    col5: '2024-03-01 00:00:00',
    col6: '2024-03-15 08:15:45',
    col7: 14,
    col8: 0.47,
    col9: 110.45,
  },
  {
    col1: '007-1-201',
    col2: '张三',
    col3: 90.55,
    col4: '物业费',
    col5: '2024-02-01 00:00:00',
    col6: '2024-02-28 18:05:20',
    col7: 28,
    col8: 0.93,
    col9: 200.34,
  },
  {
    col1: '008-1-302',
    col2: '李四',
    col3: 68.77,
    col4: '每月物业费',
    col5: '2024-01-01 00:00:00',
    col6: '2024-01-15 11:25:10',
    col7: 14,
    col8: 0.47,
    col9: 150.12,
  },
  {
    col1: '009-1-103',
    col2: '测试业主',
    col3: 72.44,
    col4: '停车费',
    col5: '2023-12-01 00:00:00',
    col6: '2023-12-20 17:40:30',
    col7: 19,
    col8: 0.63,
    col9: 115.78,
  },
  {
    col1: '010-1-203',
    col2: '王五',
    col3: 78.88,
    col4: '物业费',
    col5: '2023-11-01 00:00:00',
    col6: '2023-11-30 15:45:40',
    col7: 29,
    col8: 0.97,
    col9: 180.56,
  },
  {
    col1: '011-2-204',
    col2: '李四',
    col3: 85.65,
    col4: '停车费',
    col5: '2023-10-01 00:00:00',
    col6: '2023-10-15 14:40:50',
    col7: 14,
    col8: 0.47,
    col9: 120.45,
  },
  {
    col1: '012-2-105',
    col2: '欧阳',
    col3: 72.87,
    col4: '物业费',
    col5: '2023-09-01 00:00:00',
    col6: '2023-09-29 18:11:54',
    col7: 28,
    col8: 0.93,
    col9: 150.32,
  },
  {
    col1: '013-2-301',
    col2: '测试业主',
    col3: 79.24,
    col4: '每月物业费',
    col5: '2023-08-01 00:00:00',
    col6: '2023-08-15 18:11:54',
    col7: 14,
    col8: 0.47,
    col9: 130.78,
  },
  {
    col1: '014-2-102',
    col2: '张三',
    col3: 65.89,
    col4: '停车费',
    col5: '2023-07-01 00:00:00',
    col6: '2023-07-10 15:11:54',
    col7: 10,
    col8: 0.33,
    col9: 90.25,
  },
  {
    col1: '015-2-201',
    col2: '王五',
    col3: 75.68,
    col4: '物业费',
    col5: '2023-06-01 00:00:00',
    col6: '2023-06-29 18:11:54',
    col7: 29,
    col8: 0.97,
    col9: 150.89,
  },
  {
    col1: '016-3-101',
    col2: '李四',
    col3: 89.54,
    col4: '每月物业费',
    col5: '2023-05-01 00:00:00',
    col6: '2023-05-30 16:11:54',
    col7: 30,
    col8: 1.0,
    col9: 165.45,
  },
  {
    col1: '017-3-203',
    col2: '欧阳',
    col3: 68.45,
    col4: '停车费',
    col5: '2023-04-01 00:00:00',
    col6: '2023-04-18 18:11:54',
    col7: 18,
    col8: 0.6,
    col9: 99.43,
  },
  {
    col1: '018-3-104',
    col2: '测试业主',
    col3: 72.65,
    col4: '物业费',
    col5: '2023-03-01 00:00:00',
    col6: '2023-03-25 18:11:54',
    col7: 25,
    col8: 0.83,
    col9: 125.67,
  },
];
