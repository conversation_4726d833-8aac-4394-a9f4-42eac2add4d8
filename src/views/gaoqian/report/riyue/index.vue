<template>
  <TabXiaoQu #default="{ item }">
    <div v-show="false"> {{ item.name === '全部' ? (xiaoqu = '') : (xiaoqu = item.name) }}</div>
    <PageWrapper :dense="true">
      <template #footer>
        <Tabs default-active-key="0" v-model:activeKey="currentKey" @change="onChange">
          <Tabs.TabPane key="0" tab="日报表" />
          <Tabs.TabPane key="1" tab="月报表" />
        </Tabs>
      </template>
      <BasicTable
        @register="registerTable"
        :search-info="
          item.id && {
            courtyardId: item.id,
          }
        "
      >
        <!-- <template #tableTitle>
          <div class="w-full flex items-center justify-center">{{  }}</div></template
        > -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'total'">
            {{ getTotal(record) }}
          </template>
        </template>
        <template #headerCell="{ column }">
          <template v-if="column.key === 'date'">
            {{ newDate }}
          </template>
          <template v-if="column.key === 'biaoti'">
            {{ getTitle() }}
          </template>
        </template>
      </BasicTable>
    </PageWrapper>
  </TabXiaoQu>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@/components/Table';
  import { columns, demoData, searchFormSchema } from './riyue.data';
  import { AutoFormColProps } from '@/enums/appEnum';
  import TabXiaoQu from '../../components/TabXiaoQu.vue';
  import { getDailyReport } from '@/api/gaoqian/report';
  import { PageWrapper } from '@/components/Page';
  import { ref, watch } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import { forEach } from '../../../../utils/helper/treeHelper';
  import dayjs from 'dayjs';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Dicts, useDict } from '@/hooks/web/useDict';
  import { nextTick } from 'vue';
  import { controlDataList } from '@/api/main/system';
  import { toNumber } from 'xe-utils';
  import { add } from '@/utils/calc';
  const currentKey = ref('0'); // 默认选中“待缴费”标签页
  const { createMessage } = useMessage();
  const xiaoqu = ref('');
  function getTitle() {
    return `陕西省省级机关公有住房管理中心${xiaoqu.value}`;
    // if (currentKey.value === '0') {
    //   return '欠费日报表';
    // } else if (currentKey.value === '1') {
    //   return '欠费月报表';
    // }
  }
  watch(
    () => xiaoqu.value,
    (newVal) => {
      nextTick(() => {
        onChange(currentKey.value);
      });
    },
  );

  const nColumns = ref<any[]>([]);
  const newDate = ref(dayjs().format('YYYY年MM月DD日'));
  const [registerTable, { reload, getForm, setProps }] = useTable({
    columns: nColumns,
    api: afterGetDailyReport,
    useSearchForm: true,
    showTableSetting: false,

    pagination: false,
    bordered: true,
    showIndexColumn: false,
    beforeFetch: (params) => {
      if (!params.dateTime) {
        createMessage.error('请选择日期');
        return false;
      }
      params.params = params.params || {};
      if (currentKey.value === '0') {
        params.params.beginTime = dayjs(params.dateTime).startOf('day').format('YYYY-MM-DD 00:00:00');
        params.params.endTime = dayjs(params.dateTime).endOf('day').format('YYYY-MM-DD 23:59:59');
        newDate.value = dayjs(params.dateTime).format('YYYY年MM月DD日');
      } else if (currentKey.value === '1') {
        params.params.beginTime = dayjs(params.dateTime).startOf('month').format('YYYY-MM-DD 00:00:00');
        params.params.endTime = dayjs(params.dateTime).endOf('month').format('YYYY-MM-DD 23:59:59');
        newDate.value = dayjs(params.dateTime).format('YYYY年MM月');
      }

      return params;
    },
    afterFetch: (data) => {
      console.log(data);

      result.value = data;
    },
    formConfig: {
      labelWidth: 80,
      model: {
        dateTime: dayjs().format('YYYY-MM-DD'),
      },
      schemas: searchFormSchema,
      baseColProps: AutoFormColProps,
      autoSubmitOnEnter: true,
    },
  });
  const dict = ref<Dicts>({});
  function getTotal(record: Recordable) {
    let total = 0;
    forEach(dict.value['payment_model'], (item) => {
      total += toNumber(record[item.value]) || 0;
    });
    return total;
  }
  const init = async () => {
    const res = await useDict(['payment_model']);
    const newColumns = [...columns];
    const paymentModelDict = res['payment_model'] || [];
    paymentModelDict.forEach((item) => {
      newColumns.push({
        title: item.label,
        dataIndex: item.value,
        width: 100,
      });
    });
    newColumns.push({
      title: '日合计',
      dataIndex: 'total',
      width: 100,
    });
    nColumns.value = [
      {
        title: '',
        dataIndex: 'biaoti',
        children: newColumns,
      },
    ];
    dict.value = res;
  };
  const result = ref<Recordable[]>([]);
  async function onChange(key: any) {
    currentKey.value = key;
    if (key === '0') {
      await getForm()?.updateSchema([
        {
          field: 'dateTime',
          label: '日期',
          component: 'DatePicker',
          componentProps: {
            style: { width: '100%' },
            format: 'YYYY-MM-DD',
          },
        },
      ]);
      console.log('切换到日报表', dayjs().format('YYYY-MM-DD'));

      await getForm()?.setFieldsValue({
        dateTime: dayjs().format('YYYY-MM-DD'),
      });
    } else if (key === '1') {
      console.log('切换到月报表');

      await getForm()?.updateSchema([
        {
          field: 'dateTime',
          label: '月份',
          component: 'MonthPicker',
          componentProps: {
            style: { width: '100%' },
            format: 'YYYY-MM',
          },
        },
      ]);
      await getForm()?.setFieldsValue({
        dateTime: dayjs().format('YYYY-MM'),
      });
    }
    console.log('切换到', key, getForm()?.getFieldsValue());

    reload();
  }
  async function afterGetDailyReport(params) {
    await init();
    const res = await getDailyReport(params);
    console.log(res);
    const result: any[] = [];
    Object.keys(res).forEach((key) => {
      const item = res[key];

      const params = {
        type: key,
      };
      dict.value['payment_model'].forEach((dictItem) => {
        params[dictItem.value] = item[dictItem.value] || '0';
      });
      result.push(params);
    });
    result.push({
      date: '合计',
      type: '',
      ...dict.value['payment_model'].reduce((prev, cur) => {
        console.log(prev, cur);

        prev[cur.value] = result.reduce((prev1, cur1) => {
          console.log(prev1, cur1);

          return add(toNumber(prev1), toNumber(cur1[cur.value]));
        }, 0);
        return prev;
      }, {}),
    });
    return result;
  }
</script>

<style scoped>
  .title {
    display: flex;
    position: relative;
    padding-left: 7px;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    cursor: pointer;
    user-select: none;
  }
</style>
