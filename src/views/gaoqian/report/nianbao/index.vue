<template>
  <TabXiaoQu #default="{ item }">
    <BasicTable
      @register="registerTable"
      :search-info="
        item.id && {
          courtyardId: item.id,
        }
      "
    >
    </BasicTable>
  </TabXiaoQu>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@/components/Table';
  import { columns, searchFormSchema } from './nianbao.data';
  import { AutoFormColProps } from '@/enums/appEnum';
  import TabXiaoQu from '../../components/TabXiaoQu.vue';
  import { onMounted } from 'vue';
  import { getYearlyReport } from '@/api/gaoqian/report';
  import dayjs from 'dayjs';
  const { createMessage } = useMessage();
  import { useMessage } from '@/hooks/web/useMessage';
  const [registerTable, { reload, setColumns }] = useTable({
    columns,
    api: getYearlyReport,
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: true,
    beforeFetch: (params) => {
      if (!params.dateTime) {
        createMessage.error('请选择日期');
        return false;
      }
      return {
        ...params,
        params: {
          beginTime: dayjs(params.dateTime).startOf('year').format('YYYY-MM-DD 00:00:00'),
          endTime: dayjs(params.dateTime)
            .endOf('year')
            .format('YYYY-MM-DD' + ' 23:59:59'),
        },
      };
    },
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      baseColProps: AutoFormColProps,
      autoSubmitOnEnter: true,
    },
  });
</script>

<style scoped></style>
