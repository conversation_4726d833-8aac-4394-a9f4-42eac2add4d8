import { BasicColumn, FormSchema } from '@/components/Table';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '收费项目',
    dataIndex: 'typeView',
    width: 100,
  },
  {
    title: '应收',
    dataIndex: 'receivableAmount',
    width: 100,
  },
  {
    title: '实收',
    dataIndex: 'collectedAmount',
    width: 100,
  },
  {
    title: '欠收',
    dataIndex: 'offsetAmount',
    width: 100,
  },
  {
    title: '收款率',
    dataIndex: 'collectionRate',
    width: 100,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'dateTime',
    label: '查询年度',
    component: 'YearPicker',
    defaultValue: dayjs(),
  },
];
