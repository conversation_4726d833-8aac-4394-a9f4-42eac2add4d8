<template>
  <div>
    <Card :title="'缴费时间'">
      <Descriptions :column="3">
        <Descriptions.Item label="合同开始时间">{{ leaseStartTime }}</Descriptions.Item>
        <Descriptions.Item label="合同结束时间">{{ leaseEndTime }}</Descriptions.Item>
        <Descriptions.Item label="缴费开始时间">{{
          (props.houseInfo?.leaseIngVo?.leaseVo?.paymentStartTime &&
            dayjs(props.houseInfo?.leaseIngVo?.leaseVo?.paymentStartTime).format('YYYY年MM月DD日')) ||
          '未缴费'
        }}</Descriptions.Item>
        <Descriptions.Item label="房租已缴至" v-if="props.houseInfo?.leaseIngVo?.leaseVo?.leaseFlag">{{
          (props.houseInfo?.leaseIngVo?.leaseVo?.endRentPaymentDay &&
            dayjs(props.houseInfo?.leaseIngVo?.leaseVo?.endRentPaymentDay).format('YYYY年MM月DD日')) ||
          '未缴费'
        }}</Descriptions.Item>
        <Descriptions.Item label="物业费已缴至" v-if="props.houseInfo?.leaseIngVo?.leaseVo?.propertyFlag">{{
          (props.houseInfo?.leaseIngVo?.leaseVo?.propertyEndRentPaymentDay &&
            dayjs(props.houseInfo?.leaseIngVo?.leaseVo?.propertyEndRentPaymentDay).format('YYYY年MM月DD日')) ||
          '未缴费'
        }}</Descriptions.Item>
      </Descriptions>
    </Card>
    <Card :title="'抄表数据'" class="mt-2">
      <BasicTable @register="registerTable2"> </BasicTable>
    </Card>
    <Card :title="'未结清欠费明细'" class="mt-2">
      <BasicTable @register="registerTable">
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'yijiaoAmount'">
            {{ subtract(record.arrearsAmount, record.remainingAmount) }}
          </template>
        </template>
      </BasicTable>
    </Card>
  </div>
</template>

<script setup lang="ts">
  import {
    getLastChaobiaoData,
    getUnfinishedArrears,
    getWaterMeterTerminalListOpen,
    getWaterMeterTerminalListWithMoney,
  } from '@/api/gaoqian/manage';
  import { BasicTable, useTable } from '@/components/Table';
  import { Card, Descriptions } from 'ant-design-vue';
  import { computed, onMounted, ref } from 'vue';
  import dayjs from 'dayjs';
  import { subtract } from '@/utils/calc';
  const props = defineProps({
    houseInfo: {
      type: Object as PropType<Recordable>,
      default: {},
    },
  });

  const leaseStartTime = computed(() => {
    const time = props.houseInfo?.leaseIngVo?.leaseVo?.showStartTime || props.houseInfo?.leaseIngVo?.leaseVo?.startTime;
    if (!time) return '';
    return dayjs(time).format('YYYY年MM月DD日');
  });

  const leaseEndTime = computed(() => {
    const time = props.houseInfo?.leaseIngVo?.leaseVo?.showEndTime || props.houseInfo?.leaseIngVo?.leaseVo?.endTime;
    if (!time) return '';
    return dayjs(time).format('YYYY年MM月DD日');
  });

  const [registerTable2, { setTableData }] = useTable({
    inset: true,
    // bordered: true,
    canResize: false,
    columns: [
      {
        title: '终端名称',
        dataIndex: 'name',
        width: 100,
        customRender: ({ record }) => {
          return record?.gzfV2MeterTerminalVo?.name;
        },
      },
      {
        title: '终端类型',
        dataIndex: 'type',
        width: 100,
        customRender: ({ record }) => {
          return record?.gzfV2MeterTerminalVo?.type == 1 ? '水表' : '电表';
        },
      },
      {
        title: '抄表时间',
        dataIndex: 'liveTime',
        width: 100,
        customRender: ({ record }) => {
          return record?.gzfV2MeterTerminalVo?.liveTime;
        },
      },
      {
        title: '最新读数',
        dataIndex: 'liveValue',
        width: 100,
        customRender: ({ record }) => {
          return record?.gzfV2MeterTerminalVo?.lastReadValue;
        },
      },
      {
        title: '待缴费',
        dataIndex: 'totalValue',
        width: 100,
      },
    ],
  });
  const [registerTable] = useTable({
    inset: true,
    api: getUnfinishedArrears,
    searchInfo: {
      leaseId: props.houseInfo?.leaseIngVo?.leaseVo?.id,
    },
    // bordered: true,
    canResize: false,
    columns: [
      {
        title: '缴费项',
        dataIndex: 'typeView',
        width: 100,
      },
      {
        title: '欠费金额',
        dataIndex: 'arrearsAmount',
        width: 100,
      },
      {
        title: '已缴欠费',
        dataIndex: 'yijiaoAmount',
        width: 100,
      },
      {
        title: '剩余欠费',
        dataIndex: 'remainingAmount',
        width: 100,
      },
      {
        title: '欠费时间',
        dataIndex: 'typeView',
        width: 100,
      },
    ],
  });
  onMounted(() => {
    getWaterMeterTerminalListWithMoney({
      leaseId: props.houseInfo?.leaseIngVo?.leaseVo?.id,
    }).then((res) => {
      setTableData(res);
    });
  });
</script>

<style scoped></style>
