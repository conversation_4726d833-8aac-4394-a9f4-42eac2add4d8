<template>
  <BasicModal
    v-bind="$attrs"
    title="结算单打印预览"
    @ok="handleSubmit"
    :width="'900px'"
    :ok-text="'打印'"
    @register="registerModal"
  >
    <div id="settlement-print-table">
      <div style="position: relative; width: 210mm; margin: 0 auto; padding-bottom: 20px">
        <div style="margin-bottom: 20px; color: #000; font-size: 2.5em; font-weight: bold; text-align: center">
          退房结算清单
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
          <!-- 基本信息表格 -->
          <table style="width: 100%; margin-bottom: 0; border-collapse: collapse">
            <tr>
              <td style="width: 25%; padding: 10px; font-weight: bold; text-align: center">租户</td>
              <td style="width: 25%; padding: 10px; text-align: center">{{ settlementData.pigeonhole?.tenantName }}</td>
              <td style="width: 25%; padding: 10px; font-weight: bold; text-align: center">租赁时间</td>
              <td style="width: 25%; padding: 10px; text-align: center" @dblclick="handleEditTime">
                {{ customStartTime }}至<br />
                {{ customEndTime }}
              </td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; text-align: center">结算类型</td>
              <td style="padding: 10px; text-align: center">{{ settlementData.closeType }}</td>
              <td style="padding: 10px; font-weight: bold; text-align: center">电话</td>
              <td style="padding: 10px; text-align: center">{{ settlementData.tenantInfo?.phoneNumber }}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; text-align: center">房屋信息</td>
              <td colspan="3" style="padding: 10px; text-align: center">{{
                settlementData.pigeonhole?.houseLocation
              }}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; text-align: center">卡号</td>
              <td colspan="3" style="padding: 10px; text-align: center" @dblclick="handleEditCardNumber">
                {{ customCardNumber }}
              </td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; text-align: center">开户行</td>
              <td colspan="3" style="padding: 10px; text-align: center" @dblclick="handleEditBankName">
                {{ customBankName }}
              </td>
            </tr>
          </table>

          <!-- 退费项目表格 -->
          <table style="width: 100%; margin-top: 0; border-collapse: collapse">
            <tr>
              <td style="width: 50%; padding: 10px; border-top: none; font-weight: bold; text-align: center">退费项</td>
              <td style="width: 50%; padding: 10px; border-top: none; font-weight: bold; text-align: center"
                >退费金额（元）</td
              >
            </tr>
            <tr v-for="item in settlementData.settlementInfo?.returnSettlementDetailsVos || []">
              <td style="padding: 10px; text-align: center">{{ item.typeView }}</td>
              <td style="padding: 10px; text-align: center">{{ item.amount }}</td>
            </tr>
            <tr>
              <td style="padding: 10px; font-weight: bold; text-align: center">合计：</td>
              <td style="padding: 10px; font-weight: bold; text-align: center">
                {{ calculateTotal() }}元 ({{ numberToChineseCurrency(calculateTotal()) }})
              </td>
            </tr>
          </table>
        </div>

        <!-- 签名区域 -->
        <div class="signature-area" style="display: flex; justify-content: space-between; margin-top: 50px">
          <div style="width: 45%">
            <div>操作人：</div>
            <div style="height: 80px"></div>
          </div>
          <div style="width: 45%">
            <div>租户确认签字：</div>
            <div style="height: 80px"></div>
          </div>
        </div>

        <!-- 日期 -->
        <div class="date-area" style="margin-top: 30px; text-align: right">
          {{ formatDate(new Date(), 'YYYY 年 MM 月 DD 日') }}
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { getSettlementInfo } from '@/api/gaoqian/control';
  import printJS from 'print-js';
  import dayjs from 'dayjs';
  import { createNewPrompt } from '@/components/NewPrompt';
  import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
  import { getLeaseInfoById, getTenantTableById } from '@/api/gaoqian/houseManage';
  const settlementData = ref<Recordable>({});
  const paymentList = ref<Recordable[]>([]);
  const amountInfo = ref<Recordable>({});

  // 自定义输入的数据
  const customStartTime = ref<string>('');
  const customEndTime = ref<string>('');
  const customCardNumber = ref<string>('');
  const customBankName = ref<string>('');

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false, loading: true });

    try {
      // 直接使用传入的record数据，因为结算列表已经包含了基本信息
      settlementData.value = data.record || {};

      // 尝试获取更详细的结算信息（如果API支持）
      try {
        const detailResult = await getLeaseInfoById({
          id: data.record.id,
          params: { comm_Join: 'dict_view,oss_view,jiesuan' },
        });
        const tenantId = detailResult.tenantId;
        const tenantInfo = await getTenantTableById({ id: tenantId });
        settlementData.value = { ...settlementData.value, tenantInfo };
      } catch (apiError) {}
    } catch (error) {
      console.error('初始化结算打印数据失败:', error);
    } finally {
      setModalProps({ loading: false });
    }
  });

  // 格式化日期
  function formatDate(date: string | Date, format = 'YYYY年MM月DD日') {
    if (!date) return '--';
    return dayjs(date).format(format);
  }

  // 计算总金额
  function calculateTotal(): any {
    return (
      settlementData.value.settlementInfo?.returnSettlementDetailsVos?.reduce((total: number, item: any) => {
        return total + Number(item.amount);
      }, 0) || 0
    );
  }
  function numberToChineseCurrency(num: number) {
    // 检查输入是否为合法数字
    if (typeof num !== 'number' || isNaN(num) || num < 0) {
      console.log('请输入一个非负数字', num);

      throw new Error('请输入一个非负数字');
    }

    // 定义中文数字和单位
    const chineseNumbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '万', '亿'];
    const decimalUnits = ['角', '分'];

    if (num === 0) return '零元整';

    let integerPart = Math.floor(num); // 整数部分
    let decimalPart = Math.round((num - integerPart) * 100); // 小数部分（保留两位小数）

    let result = '';

    // 处理整数部分
    if (integerPart > 0) {
      let integerStr = '';
      let groupCount = 0; // 万、亿分组计数

      while (integerPart > 0) {
        let group = integerPart % 10000; // 每四位为一组
        integerPart = Math.floor(integerPart / 10000);

        if (group > 0) {
          let groupStr = '';
          for (let i = 0; i < 4 && group > 0; i++) {
            const digit = group % 10;
            group = Math.floor(group / 10);
            if (digit > 0) {
              groupStr = chineseNumbers[digit] + units[i] + groupStr;
            } else if (!groupStr.startsWith('零')) {
              groupStr = '零' + groupStr;
            }
          }
          // 移除末尾多余的零
          groupStr = groupStr.replace(/零+$/, '');
          integerStr = groupStr + bigUnits[groupCount] + integerStr;
        }
        groupCount++;
      }

      result += integerStr + '元';
    }

    // 处理小数部分
    if (decimalPart > 0) {
      let decimalStr = '';
      for (let i = 0; i < 2; i++) {
        const digit = Math.floor(decimalPart / (i === 0 ? 10 : 1)) % 10;
        if (digit > 0) {
          decimalStr += chineseNumbers[digit] + decimalUnits[i];
        } else if (decimalStr === '') {
          decimalStr += '零';
        }
      }
      result += decimalStr.replace(/零+$/, '');
    } else {
      result += '整';
    }

    // 移除连续多余的 "零"
    result = result.replace(/零+/g, '零');

    return result;
  }
  const currentStartTime = computed(() => dayjs(customStartTime.value).format('YYYY-MM-DD'));
  const currentEndTime = computed(() => dayjs(customEndTime.value).format('YYYY-MM-DD'));

  // 处理时间编辑
  function handleEditTime() {
    createNewPrompt({
      title: '租赁时间',
      labelWidth: 120,
      schemas: [
        {
          field: 'startTime',
          label: '开始时间',
          component: 'DatePicker',
          ...(customStartTime.value ? { defaultValue: currentStartTime.value } : {}),
          // defaultValue: currentStartTime,
          componentProps: {
            locale,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
          rules: [{ required: true, message: '请输入开始时间' }],
        },
        {
          field: 'endTime',
          label: '结束时间',
          component: 'DatePicker',
          ...(customEndTime.value ? { defaultValue: currentEndTime.value } : {}),
          // defaultValue: currentEndTime,
          componentProps: {
            locale,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
          rules: [{ required: true, message: '请输入结束时间' }],
        },
      ],
      onOK: async ({ startTime, endTime }) => {
        customStartTime.value = startTime;
        customEndTime.value = endTime;
      },
    });
  }

  // 处理卡号编辑
  function handleEditCardNumber() {
    createNewPrompt({
      title: '编辑银行卡号',
      labelWidth: 100,
      schemas: [
        {
          field: 'cardNumber',
          label: '银行卡号',
          component: 'Input',
          defaultValue: customCardNumber.value,
          componentProps: {
            placeholder: '请输入完整的银行卡号',
          },
          rules: [{ required: true, message: '请输入银行卡号' }],
        },
      ],
      onOK: async ({ cardNumber }) => {
        customCardNumber.value = cardNumber;
      },
    });
  }

  // 处理开户行编辑
  function handleEditBankName() {
    createNewPrompt({
      title: '编辑开户行',
      labelWidth: 100,
      schemas: [
        {
          field: 'bankName',
          label: '开户行',
          component: 'Input',
          defaultValue: customBankName.value,
          componentProps: {
            placeholder: '请输入完整的开户行名称',
          },
          rules: [{ required: true, message: '请输入开户行名称' }],
        },
      ],
      onOK: async ({ bankName }) => {
        customBankName.value = bankName;
      },
    });
  }

  // 打印功能
  function handleSubmit() {
    const printTable = document.querySelector('#settlement-print-table')?.innerHTML;
    if (!printTable) return;

    printJS({
      printable: `
        <div>
          <style>
            @page {
              size: A4;
              margin: 20mm;
            }

            body {
              font-family: "Microsoft YaHei", Arial, sans-serif;
              font-size: 12px;
              line-height: 1.4;
            }

            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 0;
            }

            /* 表格容器 - 统一外边框 */
            .table-container {
              border: 1px solid #000;
              display: inline-block;
              width: 100%;
            }

            /* 重置所有表格边框 */
            table {
              border: none !important;
              border-collapse: collapse;
              width: 100%;
            }

            /* 单元格内部边框 */
            td {
              padding: 10px;
              text-align: center;
              font-size: 12px;
              line-height: 18px;
              vertical-align: middle;
              border: none;
              border-right: 1px solid #000;
              border-bottom: 1px solid #000;
            }

            /* 移除最右列的右边框 */
            td:last-child {
              border-right: none;
            }

            /* 移除最后一行的下边框 */
            .table-container table:last-child tr:last-child td {
              border-bottom: none;
            }

            /* 第一个表格和第二个表格之间的分隔线 */
            .table-container table:first-child tr:last-child td {
              border-bottom: 1px solid #000;
            }

            /* 签名区域样式 */
            .signature-area {
              display: flex;
              justify-content: space-between;
              margin-top: 50px;
              border: none !important;
            }

            .signature-area div {
              width: 45%;
              border: none !important;
            }

            .signature-area, .signature-area * {
              border: none !important;
            }

            .date-area {
              margin-top: 30px;
              text-align: right;
              border: none !important;
            }

            .date-area, .date-area * {
              border: none !important;
            }

            /* 标题样式 */
            .title {
              font-size: 18px;
              font-weight: bold;
              text-align: center;
              margin-bottom: 20px;
            }

            /* 红色文字 */
            .red-text {
              color: red;
            }

            /* 粗体文字 */
            .bold-text {
              font-weight: bold;
            }
          </style>
          ${printTable}
        </div>
      `,
      type: 'raw-html',
    });
  }
</script>

<style scoped>
  table {
    width: 100%;
    margin-bottom: 12px;
    border-collapse: collapse;
  }

  th,
  td {
    padding: 8px;
    border: 1px solid #000;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
  }

  th {
    background-color: #f4f4f4;
    font-weight: bold;
  }

  .text-left {
    text-align: left !important;
  }

  .no-border {
    border: none !important;
  }
</style>
