<template>
  <n-color-picker
    v-bind="$attrs"
    :value="value"
    :show-alpha="showAlpha"
    :on-update:value="onChange"
  />
</template>
<script lang="ts" setup>
  import { Form } from 'ant-design-vue';
  import { NColorPicker } from 'naive-ui';

  defineOptions({ name: 'ColorPicker' });
  const emit = defineEmits(['change', 'update:value']);
  const props = defineProps({
    value: { type: String, default: '#ffffff' },
    showAlpha: { type: Boolean, default: false },
  });
  const formItemContext = Form.useInjectFormItemContext();

  const onChange = (value: string) => {
    emit('change', value);
    emit('update:value', value);
    formItemContext?.onFieldChange();
  };
</script>
<style lang="less" scoped>
  .n-color-picker,
  :deep(.n-color-picker-trigger) {
    min-height: 32px !important;
    border-color: var(--custom-border-color);
  }
</style>
