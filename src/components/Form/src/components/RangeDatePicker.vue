<template>
  <div class="w-full">
    <RangePicker
      v-bind="getAttrs"
      @change="onChange"
      :placeholder="['开始日期', '结束日期']"
      class="w-full"
    ></RangePicker>
  </div>
</template>

<script setup lang="ts">
  import { Form, RangePicker } from 'ant-design-vue';
  import { computed, useAttrs } from 'vue';
  import dayjs from 'dayjs';
  const attrs = useAttrs();
  const emit = defineEmits(['update:value', 'change']);
  const FormContext = Form.useInjectFormItemContext();
  const getAttrs = computed<any>(() => {
    const value = attrs?.value;
    return {
      ...attrs,
      ...(value
        ? {
            value: [value[0] && dayjs(value[0]), value[1] && dayjs(value[1])],
          }
        : {}),
      onChange,
    };
  });

  const onChange = (val) => {
    if (!val) {
      emit('update:value', val);
      emit('change', val);
      return;
    }
    const [start, end] = val;
    const startTime = start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : null;
    const endTime = end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : null;
    const newVal = [dayjs(startTime), dayjs(endTime)];
    emit('update:value', newVal);
    emit('change', newVal);
    FormContext?.onFieldChange();
  };
</script>
