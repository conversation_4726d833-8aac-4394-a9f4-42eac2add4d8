<template>
  <div>
    <BasicTable @register="register" v-bind="$attrs" @data-source-change="onDataSourceChange">
      <template v-for="item in Object.keys($slots)" #[item]="data">
        <slot :name="item" v-bind="data || {}"></slot>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, TableActionType } from '@/components/Table';
  import { nextTick, ref, unref, watch } from 'vue';
  import { FormActionType } from '../types/form';
  import { Form } from 'ant-design-vue';
  defineOptions({ name: 'FormTable', inheritAttrs: false });
  type UseTableMethod = TableActionType & {
    getForm: () => FormActionType;
  };
  const formItemContext = Form.useInjectFormItemContext();
  const tableAction = ref<TableActionType>();
  const formActions = ref<UseTableMethod>();
  const props = defineProps<{
    value?: Recordable[] | string;
  }>();
  // const
  const isString = ref(false);
  const emit = defineEmits(['register', 'update:value']);
  watch(
    () => props.value,
    (value) => {
      if (value) {
        nextTick(() => {
          if (typeof value === 'string') {
            value && unref(tableAction)?.setTableData(JSON.parse(value || '[]'));
            isString.value = true;
          } else {
            value && unref(tableAction)?.setTableData(value);
          }
        });
      }
    },
    {
      immediate: true,
    },
  );
  const register = (instance, formInstance) => {
    tableAction.value = instance;
    formActions.value = formInstance;
    emit('register', instance, formInstance);
  };
  function onDataSourceChange(datsSource: Recordable[]) {
    formItemContext?.onFieldChange();
    if (isString.value) {
      emit('update:value', JSON.stringify(datsSource));
    } else {
      emit('update:value', datsSource);
    }
  }
</script>

<style scoped></style>
