<template>
  <div>
    <span v-if="['Input', 'InputTextArea'].includes(component)">{{ getValue }}</span>
    <span v-if="['DatePicker'].includes(component)">{{ getDateValue }}</span>
    <span v-if="['Select', 'RadioGroup'].includes(component)">{{ getOptionValue }}</span>
    <UserPicker v-if="component === 'UserPicker'" v-model:value="getValue" mode="detail"></UserPicker>
  </div>
</template>

<script setup lang="ts">
  defineOptions({ name: 'DetailItem', inheritAttrs: false });
  import { computed, useAttrs } from 'vue';
  import dayjs from 'dayjs';
  import { UserPicker } from '@/components/UserPicker';

  const attrs: Recordable = useAttrs();
  const component = computed<string>(() => {
    return attrs.schema.component;
  });
  const getValue = computed(() => {
    return attrs.formModel[attrs.schema.field];
  });
  const getDateValue = computed(() => {
    return dayjs(attrs.formModel[attrs.schema.field]).format('YYYY-MM-DD HH:mm:ss');
  });
  const getOptionValue = computed(() => {
    const options = attrs.schema?.componentProps?.options;

    return options?.find((item) => item.value === attrs.formModel[attrs.schema.field])?.label;
  });
</script>

<style scoped></style>
