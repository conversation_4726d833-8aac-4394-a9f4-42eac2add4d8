<!--
 * @Author: wang<PERSON><PERSON>
 * @Date: 2024-03-07 16:34:30
 * @LastEditors: wangfei
 * @LastEditTime: 2024-03-13 16:21:56
 * @Descripttion: 富文本编辑器
-->
<template>
  <div class="editor">
    <Toolbar class="editor-toolbar" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor
      style="height: 400px; min-height: 400px"
      :defaultConfig="editorConfig"
      :mode="mode"
      :model-value="value"
      @on-created="onCreated"
      @on-change="onchange"
      @custom-paste="customPaste"
    />
  </div>
</template>
<script lang="ts" setup>
  import '@wangeditor/editor/dist/css/style.css';
  import { ref, watchEffect, onBeforeUnmount, shallowRef } from 'vue';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import { ossUpload } from '@/api/main/system';
  import { useMessage } from '@/hooks/web/useMessage';
  import registerMenuModule from './index';
  import { IDomEditor } from '@wangeditor/editor';
  defineOptions({ name: 'BasicEditor' });
  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
  });
  const emits = defineEmits(['update:value', 'onPrevieClick']);
  const mode = ref('default');
  // 编辑器实例，必须用 shallowRef，重要！
  const editorRef = shallowRef();
  const { createMessage } = useMessage();

  // 工具栏选项
  const toolbarConfig = {
    // 排除掉某些菜单
    excludeKeys: [
      'fullScreen', //全屏
      'emotion', //表情
      'group-more-style',
    ],
  };
  function removeImgTags(htmlString) {
    // 使用正则表达式匹配 img 标签并将其替换为空字符串
    const imgTagRegex = /<img\b[^>]*>/gi;
    return htmlString.replace(imgTagRegex, '');
  }
  function customPaste(editor: IDomEditor, event: ClipboardEvent, callback): void {
    let html = event.clipboardData?.getData('text/html');
    if (html) {
      const res = removeImgTags(html);
      editor.dangerouslyInsertHtml(res);
      event.preventDefault();
      callback(false);
      return;
    }

    callback(true);
  }
  // 编辑器选项
  const maxFileSize = 30 * 1024 * 1024; // 30M -文件最大上传大小
  const editorConfig = {
    placeholder: '请输入内容...',
    MENU_CONF: {
      // 配置上传图片
      uploadImage: {
        // 自定义上传，insertFn是上传成功后插入图片到编辑器
        async customUpload(file: File, insertFn: any) {
          if (file.size > maxFileSize) {
            createMessage.error('所选择文件大于30M，请重新选择~');
            return false;
          }
          createMessage.loading('上传中...');
          // 创建 FormData 对象
          const formData = new FormData();
          formData.append('file', file);
          try {
            const res = await ossUpload(formData);
            createMessage.destroy();
            if (res.code === 200) {
              insertFn(res.data.url);
            } else {
              createMessage.error('上传失败，请重试~');
            }
          } catch (e: any) {
            console.log(e);
            createMessage.error('上传失败，请重试~');
          }
        },
      },
      // 上传视频
      uploadVideo: {
        // 自定义上传，insertFn是上传成功后插入视频到编辑器
        async customUpload(file: File, insertFn: any) {
          // TS 语法
          if (file.size > maxFileSize) {
            createMessage.error('所选择文件大于30M，请重新选择~');
            return false;
          }
          createMessage.loading('上传中...');
          // 创建 FormData 对象
          const formData = new FormData();
          formData.append('file', file);
          try {
            const res = await ossUpload(formData);
            createMessage.destroy();
            if (res.code === 200) {
              insertFn(res.data.url);
            } else {
              createMessage.error('上传失败，请重试~');
            }
          } catch (e: any) {
            console.log(e);
            createMessage.error('上传失败，请重试~');
          }
        },
      },
    },
  };

  const onCreated = (editor: any) => {
    editorRef.value = editor; // 记录 editor 实例，重要！
    registerMenuModule(editor, toolbarConfig);
    // 初始化菜单事件
    editor.on('previeClick', () => {
      if (editor.isEmpty()) {
        createMessage.error('预览内容不能为空~');
        return false;
      }
      emits('onPrevieClick', editor.getHtml());
    });
  };

  // 编辑器回调函数
  const onchange = (res: { getHtml: () => any }) => {
    emits('update:value', res.getHtml());
  };

  watchEffect(() => {
    if (!props.value && editorRef.value) {
      editorRef.value?.clear();
    }
  });

  // 组件销毁时，也及时销毁编辑器，重要！
  onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy(); // 销毁
  });
</script>
<style lang="less" scoped>
  .editor {
    border: 1px solid var(--w-e-textarea-border-color);
    border-radius: 6px;
    background-color: var(--w-e-toolbar-bg-color);

    .editor-toolbar {
      border-bottom: 1px solid var(--w-e-textarea-border-color);
    }

    :deep(.w-e-bar) {
      border-radius: 6px 6px 0 0;
    }

    :deep(.w-e-text-container) {
      border-radius: 0 0 6px 6px;
    }
  }
</style>
