<!--
 * @Author: wang<PERSON><PERSON>
 * @Date: 2024-03-07 16:25:16
 * @LastEditors: wangfei
 * @LastEditTime: 2024-03-21 16:48:14
 * @Descripttion: 公告预览弹窗
-->
<template>
  <BasicModal
    width="50rem"
    v-bind="$attrs"
    title="预览"
    okText="关闭"
    :destroyOnClose="true"
    :canFullscreen="false"
    :showCancelBtn="false"
    @register="registerModal"
    @ok="closeModal"
  >
    <div class="editorContent">
      <div v-html="editorHtmlValue"></div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { useModalInner, BasicModal } from '@/components/Modal';

  const editorHtmlValue = ref('');
  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    editorHtmlValue.value = data.editorHtmlValue;
  });
</script>
