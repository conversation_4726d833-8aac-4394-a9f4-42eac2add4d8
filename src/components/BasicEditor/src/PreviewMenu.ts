/*
 * @Author: wang<PERSON><PERSON>
 * @Date: 2024-03-08 15:59:25
 * @LastEditors: wangfei
 * @LastEditTime: 2024-03-08 16:58:25
 * @Descripttion: 预览按钮
 */
import { IButtonMenu, IDomEditor } from '@wangeditor/editor';

class PreviewMenu implements IButtonMenu {
  tag: string;
  title: string;
  iconSvg: string;
  constructor() {
    this.title = '预览'; // 自定义菜单标题
    this.tag = 'button';
    this.iconSvg =
      '<svg t="1709888285272" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1822" width="200" height="200"><path d="M511.875152 661.442575a339.668699 339.668699 0 0 0 262.179957-149.817118 304.628139 304.628139 0 0 0-524.359913 0 339.668699 339.668699 0 0 0 262.179956 149.817118z m0 71.163131a424.981224 424.981224 0 0 1-329.597659-183.609201 58.262212 58.262212 0 0 1 0-74.159473 387.610176 387.610176 0 0 1 659.27855 0 58.678371 58.678371 0 0 1 0 74.159473A424.481834 424.481834 0 0 1 511.875152 732.272779z m0 0" fill="#3C3D3D" p-id="1823"></path><path d="M511.875152 543.836138a31.960985 31.960985 0 1 0-31.960985-31.960986 31.960985 31.960985 0 0 0 31.960985 31.960986z m0 82.149719a114.110705 114.110705 0 1 1 114.277169-114.027473 114.3604 114.3604 0 0 1-114.277169 114.027473z m0 0" fill="#3C3D3D" p-id="1824"></path><path d="M82.232951 941.434122V778.050232a41.116476 41.116476 0 1 0-82.232951 0v177.117126a68.582947 68.582947 0 0 0 68.582947 68.582947h177.117126a41.116476 41.116476 0 1 0 0-82.232952z m0-859.201171h163.38389a41.116476 41.116476 0 1 0 0-82.232951H68.582947A68.582947 68.582947 0 0 0 0 68.582947v177.117126a41.116476 41.116476 0 1 0 82.232951 0z m859.201171 0v163.38389a41.116476 41.116476 0 1 0 82.232951 0V68.582947A68.582947 68.582947 0 0 0 955.167358 0H778.050232a41.116476 41.116476 0 1 0 0 82.232951z m0 859.201171H778.050232a41.116476 41.116476 0 1 0 0 82.232951h177.117126a68.582947 68.582947 0 0 0 68.582947-68.582947V778.050232a41.116476 41.116476 0 1 0-82.232952 0z" fill="#3C3D3D" p-id="1825"></path></svg>';
  }

  getValue(editor: IDomEditor): string | boolean {
    return '';
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(editor: IDomEditor): boolean {
    return false;
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor: IDomEditor): boolean {
    return false;
  }

  // 点击菜单时触发的函数
  exec(editor: IDomEditor) {
    if (this.isDisabled(editor)) return;
    editor.emit('previeClick');
  }
}

export default PreviewMenu;
