/*
 * @Author: wang<PERSON><PERSON>
 * @Date: 2024-03-08 16:08:13
 * @LastEditors: wangfei
 * @LastEditTime: 2024-03-08 16:51:49
 * @Descripttion: 初始化自定义按钮
 */
import PreviewMenu from './PreviewMenu';
import { Boot, IDomEditor } from '@wangeditor/editor';

const MenusList = [
  {
    key: 'PreviewMenu',
    class: PreviewMenu,
    index: 40, // 在工具栏上的顺序
  },
];

const registerMenuModule = (editor: IDomEditor, toolbarConfig: any) => {
  const toolbarMenus = editor.getAllMenuKeys(); // 获取所有菜单
  const keys = MenusList.map((item) => item.key);

  MenusList.forEach((item) => {
    if (!toolbarMenus.includes(item.key)) {
      //如果未注册则注册，此处注册的菜单必须跟toolbarConfig默认配置的菜单不一致
      Boot.registerMenu({
        key: item.key,
        factory() {
          return new item.class();
        },
      });
    }
  });

  toolbarConfig.insertKeys = {
    index: MenusList[0].index,
    keys,
  };
};

export default registerMenuModule;
