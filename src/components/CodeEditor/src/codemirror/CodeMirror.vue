<template>
  <Codemirror
    class="h-full"
    :model-value="props.value"
    :extensions="extensions"
    :autofocus="true"
    :disabled="props.readonly"
    :indent-with-tab="false"
    @change="onChange"
    :style="{
      '--cm-border-radius': props?.style?.borderRadius,
      outline: 'unset !important',
      backgroundColor: 'var(--custom-bg-color)!important',
      minHeight: '5rem',
      ...(props.bordered
        ? {
            '--cm-border-radius': props?.style?.borderRadius || '6px',
            border: '1px solid var(--custom-border-color)',
            borderRadius: '6px',
          }
        : {}),
      ...props.style,
    }"
  />
</template>

<script lang="ts" setup>
  import { Codemirror } from 'vue-codemirror';
  import { json } from '@codemirror/lang-json';
  import { javascript } from '@codemirror/lang-javascript';
  import { html } from '@codemirror/lang-html';
  import { oneDark } from '@codemirror/theme-one-dark';
  import { computed, type PropType } from 'vue';
  import { useAppStore } from '@/store/modules/app';
  import { MODE } from './../typing';

  const props = defineProps({
    mode: {
      type: String as PropType<MODE>,
      default: MODE.JSON,
      validator(value: any) {
        // 这个值必须匹配下列字符串中的一个
        return Object.values(MODE).includes(value);
      },
    },
    value: { type: String, default: '' },
    readonly: { type: Boolean, default: false },
    bordered: { type: Boolean, default: false },
    style: { type: Object },
  });
  const appStore = useAppStore();
  const extensions = computed(() => {
    switch (props.mode) {
      case MODE.JSON:
        return appStore.getDarkMode === 'dark' ? [json(), oneDark] : [json()];
      case MODE.JS:
        return appStore.getDarkMode === 'dark' ? [javascript(), oneDark] : [javascript()];
      case MODE.HTML:
        return appStore.getDarkMode === 'dark' ? [html(), oneDark] : [html()];
      default:
        return appStore.getDarkMode === 'dark' ? [json(), oneDark] : [json()];
    }
  });
  const emit = defineEmits(['change', 'update:value']);
  function onChange(val) {
    emit('change', val);
    emit('update:value', val);
  }
</script>
<style>
  .cm-scroller {
    .cm-gutters {
      border-radius: var(--cm-border-radius);
    }
  }
</style>
