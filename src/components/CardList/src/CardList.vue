<template>
  <div class="card-list bg-white">
    <div class="flex justify-end space-x-2 card-list-header" style="padding-bottom: 8px">
      <component :is="title" />
    </div>
    <ScrollContainer class="card-list-body">
      <List :grid="{ gutter: 5, xs: 1, sm: 2, md: 2, lg: 2, xl: 4, xxl: 5 }" :data-source="attrs.dataSource">
        <template #renderItem="{ item }">
          <ListItem>
            <slot name="cardItem" v-bind="{ record: item, columns: attrs.columns }"></slot>
          </ListItem>
        </template>
      </List>
    </ScrollContainer>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, unref } from 'vue';
  import { List } from 'ant-design-vue';
  // import { useSlider } from './data';
  import { useAttrs } from '@vben/hooks';
  import { ScrollContainer } from '@/components/Container';

  defineOptions({ inheritAttrs: false });
  const ListItem = List.Item;

  // 获取slider属性
  // const sliderProp = computed(() => useSlider(4));
  const title = ref(null);
  // function sliderChange(n) {}
  const attrs = useAttrs();
  onMounted(() => {
    title.value = unref(attrs).title;
  });
  // 自动请求并暴露内部方法
</script>
<style>
  .card-list {
    padding: 6px;
    border-radius: 2px;
  }
</style>
