<template>
  <div :class="prefixCls">
    <template v-if="insert">
      <DateSelectPanel
        insert
        :value="currentSelect"
        :disabledDate="disabledDate"
        @confirm="handleConfirm"
      ></DateSelectPanel>
    </template>
    <template v-else>
      <Popover v-model:open="visible" trigger="click" placement="bottomLeft" :overlayClassName="`${prefixCls}-popover`">
        <Input placeholder="点击选择日期" :value="currentSelect.join(',')" :disabled="props.disabled" />
        <template #content>
          <DateSelectPanel
            :value="currentSelect"
            :disabledDate="disabledDate"
            @close="handleClose"
            @confirm="handleConfirm"
          ></DateSelectPanel>
        </template>
      </Popover>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import { Form, Input, Popover } from 'ant-design-vue';
  import DateSelectPanel from './components/DateSelectPanel.vue';
  import { isArray, isString } from '@/utils/is';
  import { basicProps } from './props';

  const props = defineProps({
    ...basicProps,
    insert: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    disabledDate: {
      type: Function as PropType<(date: any) => boolean>,
    },
  });

  const currentSelect = ref<string[]>([]);
  const emit = defineEmits(['change', 'update:value']);
  const formItemContext = Form.useInjectFormItemContext();
  const { prefixCls } = useDesign('date-multiple');
  const visible = ref<boolean>(false);

  const handleClose = () => {
    visible.value = false;
  };

  const handleConfirm = (dates) => {
    currentSelect.value = dates;
    emit('update:value', props.serialization ? dates.join(',') : dates);
    emit('change', props.serialization ? dates.join(',') : dates);
    formItemContext?.onFieldChange();
    if (!props.insert) handleClose();
  };

  watch(
    () => props.value,
    (value) => {
      if (isArray(value)) {
        currentSelect.value = value;
      } else if (isString(value)) {
        if (props.serialization) {
          currentSelect.value = value.split(',');
        } else {
          currentSelect.value = [value];
        }
      } else {
        currentSelect.value = [];
      }
    },
    { immediate: true },
  );
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-date-multiple';

  .@{prefix-cls} {
    &-popover {
      width: 400px;

      .ant-popover-inner {
        padding: 0;
      }
    }
  }
</style>
