<template>
  <div class="picker-layout" :style="{ '--hover-bg-color': hoverColor }">
    <div class="picker-layout-panel flex" :class="{ 'picker-layout-insert': insert }">
      <Calendar class="picker-date-panel" :fullscreen="false" :disabledDate="disabledDate" @select="handleSelect">
        <template #headerRender="{ value: current, onChange }">
          <div class="picker-date-panel-header">
            <button @click="onChange(current.year(current.year() - 1))">
              <Icon icon="ant-design:double-left-outlined" :size="14" />
            </button>
            <button @click="onChange(current.month(current.month() - 1))">
              <Icon icon="ant-design:left-outlined" :size="14" />
            </button>
            <div class="picker-date-panel-header-view">{{ current.year() }}年 {{ current.month() + 1 }}月 </div>
            <button @click="onChange(current.month(current.month() + 1))">
              <Icon icon="ant-design:right-outlined" :size="14" />
            </button>
            <button @click="onChange(current.year(current.year() + 1))">
              <Icon icon="ant-design:double-right-outlined" :size="14" />
            </button>
          </div>
        </template>
        <template #dateFullCellRender="{ current }">
          <div class="data-cell-inner" :class="{ isTody: isToday(current), active: isIncludeDay(current) }">{{
            current.date()
          }}</div>
        </template>
      </Calendar>

      <div class="picker-date-select">
        <div class="picker-date-select-header"> 所选日期 </div>
        <ScrollContainer ref="wrapperRef">
          <div class="tags">
            <Tag
              class="tag"
              closable
              v-for="item in days"
              :key="item"
              :color="getThemeColor"
              @close="() => handleUnSelect(item)"
              ><span>{{ item }}</span>
            </Tag>
          </div>
        </ScrollContainer>
      </div>
    </div>

    <div class="picker-layout-footer" v-if="!insert">
      <Space class="picker-layout-footer-btns">
        <Button size="small" @click="emit('close')">取消</Button>
        <Button size="small" type="primary" @click="handleConfirm">确定</Button>
      </Space>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, unref, watch } from 'vue';
  import { Calendar, Tag, Button, Space } from 'ant-design-vue';
  import { useRootSetting } from '@/hooks/setting/useRootSetting';
  import { ScrollContainer } from '@/components/Container';
  import Icon from '@/components/Icon/Icon.vue';
  import { lighten } from '@/utils/color';
  import dayjs, { Dayjs } from 'dayjs';

  const emit = defineEmits(['confirm', 'close']);
  const props = defineProps({
    value: {
      type: Array as PropType<string[]>,
      default: [],
    },
    insert: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    disabledDate: {
      type: Function as PropType<(date: Dayjs) => boolean>,
    },
  });
  const { getThemeColor } = useRootSetting();
  const days = ref<Array<any>>([]);
  const hoverColor = computed(() => lighten(unref(getThemeColor), 36));

  function handleConfirm() {
    emit('confirm', unref(days));
  }

  function handleUnSelect(key) {
    const newValue = unref(days).filter((item) => item !== key);
    days.value = [...newValue];
  }

  const handleSelect = (date, { source }) => {
    if (source === 'date') {
      if (isIncludeDay(date)) {
        days.value = days.value.filter((item) => item !== dayjs(date).format('YYYY-MM-DD'));
      } else {
        const strDate = dayjs(date).format('YYYY-MM-DD');
        days.value = [...days.value, strDate];
        days.value.sort((a, b) => dayjs(a).valueOf() - dayjs(b).valueOf());
      }
    }
    if (props.insert) handleConfirm();
  };

  const isIncludeDay = (date) => {
    return unref(days).includes(dayjs(date).format('YYYY-MM-DD'));
  };

  const isToday = (date) => {
    return dayjs(date).format('YYYY-MM-DD') === dayjs(new Date()).format('YYYY-MM-DD');
  };

  watch(
    () => props.value,
    (value) => {
      days.value = value;
    },
    { immediate: true },
  );
</script>

<style lang="less" scoped>
  .picker-layout {
    &-panel {
      display: flex;
      align-items: stretch;
      border-bottom: 1px solid var(--border-color);
      background: transparent;

      .picker-date-panel {
        flex-shrink: 0;
        width: 286px;
        border-right: 1px solid var(--border-color);
        border-radius: 0;
        background-color: transparent;

        :deep(.ant-picker-panel) {
          border-radius: 0;
          background: transparent;

          .ant-picker-body {
            padding: 8px 12px !important;
          }

          .ant-picker-content th {
            padding: 4px 0;
          }

          .data-cell-inner {
            display: inline-block;
            position: relative;
            z-index: 2;
            min-width: 24px;
            height: 24px;
            border-radius: 4px;
            line-height: 24px;

            &.active {
              background: var(--main-theme-color);
              color: #fff;
            }

            &.isTody::after {
              content: '';
              position: absolute;
              top: 2px;
              right: 2px;
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background: var(--main-theme-color);
            }

            &.isTody.active::after {
              background: #fff;
            }

            &:hover {
              background: var(--hover-bg-color);
            }
          }
        }

        &-header {
          display: flex;
          padding: 0 8px;

          &-view {
            flex: auto;
            font-weight: 600;
            line-height: 40px;
            text-align: center;
            user-select: none;
          }

          > button {
            min-width: 1.6em;
            padding: 0;
            transition: color 0.2s;
            border: 0;
            background: transparent;
            color: var(--custom-text-color);
            font-size: 14px;
            line-height: 40px;
            cursor: pointer;

            &:hover {
              color: var(--custom-hover-text-color);
            }
          }
        }
      }

      .picker-date-select {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 314px;

        &-header {
          padding: 0 12px;
          border-bottom: 1px solid var(--border-color);
          line-height: 40px;
        }

        .scrollbar {
          height: 100%;
        }

        .tags {
          padding: 6px;
        }

        .tag {
          width: 94px;
          margin: 4px;
        }
      }
    }

    &-insert {
      border: 1px solid var(--custom-border-color);
      border-radius: 6px;
    }

    &-footer {
      width: min-content;
      min-width: 100%;
      line-height: 38px;
      text-align: center;

      &-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 42px;
        padding: 4px 12px;
      }
    }
  }
</style>
