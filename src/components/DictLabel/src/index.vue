<template>
  <template v-for="item in dictOptions">
    <template v-if="item.value == dictValue"> {{ item.label }}</template>
  </template>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  const props = defineProps({
    value: {
      type: [String, Number],
      default: '',
    },
    options: {
      type: Array as PropType<Array<{ label: string; value: string | number }>>,
      default: () => [],
    },
  });

  const dictOptions = computed(() => props.options);
  const dictValue = computed(() => props.value);
</script>

<style scoped></style>
