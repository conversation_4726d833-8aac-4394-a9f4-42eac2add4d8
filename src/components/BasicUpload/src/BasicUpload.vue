<template>
  <div>
    <div class="upload-btns w-full flex justify-between">
      <Button class="mr-2" type="primary" block :disabled="!showUpload || disabled" @click="() => openUploadModal()">
        <template #icon>
          <Icon icon="carbon:cloud-upload" />
        </template>
        上传文件
      </Button>
      <Tooltip placement="bottom">
        <template #title>
          已上传
          <template v-if="fileList.length">
            {{ fileList.length }}
          </template>
        </template>
        <Button class="flex-shrink-0" @click="() => openPreviewModal()">
          <template #icon>
            <Icon icon="bi:eye" />
          </template>
          <template v-if="fileList.length">
            {{ fileList.length }}
          </template>
        </Button>
      </Tooltip>
    </div>

    <UploadModal
      v-bind="bindValue"
      :previewFileList="ossList"
      @register="registerUploadModal"
      @change="handleChange"
      @delete="handleDelete"
    />

    <UploadPreviewModal
      :value="ossList"
      :disabled="disabled"
      @register="registerPreviewModal"
      @list-change="handlePreviewChange"
      @delete="handlePreviewDelete"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, unref, computed, useAttrs } from 'vue';
  import { Recordable } from '@vben/types';
  import Icon from '@/components/Icon/Icon.vue';
  import { Tooltip, Button } from 'ant-design-vue';
  import { useModal } from '@/components/Modal';
  import { basicUploadProps } from './props';
  import { omit } from 'lodash-es';
  import UploadModal from './components/UploadModal.vue';
  import UploadPreviewModal from './components/UploadPreviewModal.vue';

  defineOptions({ name: 'BasicUpload' });
  const props = defineProps(basicUploadProps);

  const emit = defineEmits(['change', 'delete', 'preview-delete', 'update:value', 'update:ossList']);

  const attrs = useAttrs();

  // 上传modal
  const [registerUploadModal, { openModal: openUploadModal }] = useModal();

  //   预览modal
  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

  const fileList = ref<Array<string | number>>([]);

  const ossList = ref<Recordable[]>([]);

  const showUpload = computed(() => {
    return fileList.value.length < props.maxNumber || props.maxNumber === 0;
  });
  const bindValue = computed(() => {
    const value = { ...attrs, ...props };
    return omit(value, 'onChange');
  });

  watch(
    () => props.value,
    (value) => {
      fileList.value = value || [];
    },
    { immediate: true },
  );
  watch(
    () => props.ossList,
    (value) => {
      ossList.value = value;
    },
    { immediate: true },
  );
  // 上传modal保存操作
  function handleChange(records: Recordable[]) {
    const ids = records.map((item) => item.ossId);
    fileList.value = [...unref(fileList), ...(ids || [])];
    ossList.value = [...unref(ossList), ...records];
    emit('update:value', fileList.value);
    emit('update:ossList', ossList.value);
    emit('change', fileList.value, ossList.value);
  }

  // 预览modal保存操作
  function handlePreviewChange(records: Recordable[]) {
    const ids = records.map((item) => item.ossId);
    fileList.value = ids;
    ossList.value = records;
    emit('update:value', fileList.value);
    emit('update:ossList', ossList.value);
    emit('change', fileList.value, ossList.value);
  }

  function handleDelete(record: Recordable<any>) {
    emit('delete', record);
  }

  function handlePreviewDelete(record: Recordable) {
    emit('preview-delete', record);
  }
</script>

<style lang="less" scoped>
  .upload-btns .ant-btn.ant-btn-icon-only {
    width: 48px !important;
  }
</style>
