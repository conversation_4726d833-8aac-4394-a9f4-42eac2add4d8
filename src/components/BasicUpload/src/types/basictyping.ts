import { BasicColumn } from '@/components/Table';

export enum UploadResultStatus {
  DONE = 'done',
  SUCCESS = 'success',
  ERROR = 'error',
  UPLOADING = 'uploading',
}

export enum UploadFileType {
  IMAGE = '.jpg|.jpeg|.png|.gif|.webp',
  VIDEO = '.mp4|.avi|.mov|.mkv',
  AUDIO = '.mp3|.wav|.aac',
  WORD = '.doc|.docx',
  EXCEL = '.xls|.xlsx',
  TEXT = '.txt|.text',
  PPT = '.ppt|.pptx',
  PDF = '.pdf',
  ZIP = '.rar|.zip|.7z|.gz|.tar',
  MARKDOWN = '.md|.markdown',
}

export interface FileItem {
  thumbUrl?: string;
  name: string;
  size: string | number;
  type?: string;
  percent: number;
  file: File;
  status?: UploadResultStatus;
  response?: Recordable;
  uuid: string;
}

export interface PreviewFileItem {
  url: string;
  type: string;
  ossId?: string;
  thumbnail?: string;
  originalName?: string;
  fileSuffix?: string;
}

export interface FileBasicColumn extends Omit<BasicColumn, 'customRender'> {
  /**
   * Renderer of the table cell. The return value should be a VNode, or an object for colSpan/rowSpan config
   * @type Function | ScopedSlot
   */
  customRender?: Function;
  /**
   * Title of this column
   * @type any (string | slot)
   */
  title: string;

  /**
   * Display field of the data record, could be set like a.b.c
   * @type string
   */
  dataIndex: string;
}
