import { BasicColumn, FormSchema } from '@/components/Table';

export const myColumns: BasicColumn[] = [
  {
    title: '原名',
    dataIndex: 'originalName',
    width: 200,
  },
  {
    title: '文件后缀',
    dataIndex: 'fileSuffix',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '上传人',
    dataIndex: 'createBy',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'originalName',
    label: '原名',
    component: 'Input',
  },
];
