<template>
  <span class="thumb">
    <Image v-if="fileType === 'Image'" :src="fileUrl" :width="60" :height="60" :preview="{ src: previewUrl }" />
    <Icon v-else :icon="fileType" size="32" color="var(--main-theme-color)" />
  </span>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import { Image } from 'ant-design-vue';
  import { propTypes } from '@/utils/propTypes';
  import Icon from '@/components/Icon/Icon.vue';
  import { getFileType } from '../helper';

  const props = defineProps({
    fileUrl: propTypes.string.def(''),
    previewUrl: propTypes.string.def(''),
    fileName: propTypes.string.def(''),
  });
  const fileType = computed(() => getFileTypeFromExtension(props.fileName));

  const getFileTypeFromExtension = (extension: string): string => {
    if (!extension) {
      return 'ant-design:file-unknown-twotone';
    }
    const fileTypes = new Map([
      ['Pdf', 'ant-design:file-pdf-twotone'],
      ['Word', 'ant-design:file-word-twotone'],
      ['Excel', 'ant-design:file-excel-twotone'],
      ['Text', 'ant-design:file-text-twotone'],
      ['Zip', 'ant-design:file-zip-twotone'],
      ['Markdown', 'ant-design:file-markdown-twotone'],
      ['Video', 'ant-design:video-camera-twotone'],
      ['Audio', 'ant-design:audio-twotone'],
      ['Unknown', 'ant-design:file-twotone'],
      ['Image', 'Image'],
    ]);

    // 通过映射查找文件类型
    const fileType = fileTypes.get(getFileType(extension));
    return fileType || 'ant-design:file-twotone';
  };
</script>
<style lang="less">
  .thumb {
    img {
      display: block;
      position: static;
      border-radius: 4px;
      cursor: zoom-in;
      object-fit: cover;
    }
  }
</style>
