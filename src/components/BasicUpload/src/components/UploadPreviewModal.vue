<template>
  <BasicModal
    class="upload-preview-modal"
    width="800px"
    title="预览"
    v-bind="$attrs"
    cancelText="关闭"
    :showOkBtn="false"
    @register="register"
  >
    <FileList :dataSource="fileListRef" :columns="columns" :actionColumn="actionColumn" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { computed, ref, unref, watch } from 'vue';
  import { BasicColumn } from '@/components/Table';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { FileBasicColumn, PreviewFileItem } from '../types/basictyping';
  import { createPreviewColumns, createPreviewActionColumn } from './data';
  import { downloadFileByUrl } from '@/utils/file/download';
  import FileList from './FileList.vue';
  import { getOssListByIds } from '@/api/common';

  const props = defineProps({
    value: {
      type: Array as PropType<PreviewFileItem[] | Array<any>>,
      default: () => [],
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });

  const emit = defineEmits(['list-change', 'register', 'delete']);
  const [register] = useModalInner(async (data) => {
    if (data.ids) {
      getOssListByIds(data.ids).then((res) => {
        fileListRef.value = res.map((item) => {
          if (item.thumbnail) {
            item.thumbnail = item.thumbnail.includes('base64,')
              ? item.thumbnail
              : `data:image/png;base64,${item.thumbnail}`;
          }
          return item;
        });
      });
    }
  });
  const columns: BasicColumn[] | FileBasicColumn[] = createPreviewColumns();
  const fileListRef = ref<PreviewFileItem[]>([]);
  const actionColumn = computed(() =>
    createPreviewActionColumn({
      handleRemove,
      handleDownload,
      disabled: props.disabled,
    }),
  );
  watch(
    () => props.value,
    (value) => {
      fileListRef.value = value.map((item) => {
        if (item.thumbnail) {
          item.thumbnail = item.thumbnail.includes('base64,')
            ? item.thumbnail
            : `data:image/png;base64,${item.thumbnail}`;
        }
        return item;
      });
    },
  );
  // 删除
  function handleRemove(record: PreviewFileItem) {
    if (!record) return;
    fileListRef.value = fileListRef.value.filter((item) => item.ossId !== record.ossId);
    emit('delete', record);
    emit('list-change', unref(fileListRef));
  }

  // 下载
  function handleDownload(record: PreviewFileItem) {
    const { url = '', originalName = '' } = record;
    downloadFileByUrl({ url, fileName: originalName });
  }
</script>
<style lang="less">
  .upload-preview-modal {
    .ant-upload-list {
      display: none;
    }

    .ant-table-wrapper .ant-spin-nested-loading {
      padding: 0;
    }
  }
</style>
