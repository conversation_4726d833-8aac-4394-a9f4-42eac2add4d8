<template>
  <BasicModal width="82rem" v-bind="$attrs" title="选择文件" @register="registerUploadModal" @ok="handleOk">
    <div>
      <BasicTable @register="registerTable">
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'action'">
            <TableAction
              :actions="[
                {
                  label: '下载',
                  icon: 'ant-design:download-outlined',
                  onClick: handleDownload.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { getMyUploadFile } from '@/api/common';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { BasicTable, Key, useTable, TableAction } from '@/components/Table';
  import { AutoModalFormColProps } from '@/enums/appEnum';
  import { myColumns, searchFormSchema } from './basicfile.data';
  import { ref } from 'vue';
  import { ossDownload } from '@/api/main/system';
  const selectRowKeys = ref<Key[]>([]);
  const selectRows = ref<Recordable[]>([]);
  const [registerTable, { setProps }] = useTable({
    title: '',
    formConfig: {
      labelWidth: 80,
      baseColProps: AutoModalFormColProps,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
    },
    useSearchForm: true,
    showTableSetting: true,
    rowSelection: {
      onChange(selectedRowKeys, selectedRow) {
        selectRowKeys.value = selectedRowKeys;
        selectRows.value = selectedRow;
      },
      type: 'checkbox',
    },
    bordered: true,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
    beforeFetch: (params) => {
      params.isAsc = 'desc';
      params.orderByColumn = 'createTime';
      return params;
    },
  });
  const emit = defineEmits(['register', 'success']);
  const [registerUploadModal, { closeModal }] = useModalInner(async (data) => {
    const isMe = data?.isMe;
    const contentType = data?.contentType;

    if (isMe) {
      setProps({
        api: getMyUploadFile,
        columns: myColumns,
        rowKey: 'ossId',
        searchInfo: {
          params: {
            contentType: contentType,
          },
        },
      });
    }
  });
  function handleDownload(record) {
    ossDownload(record.ossId).then((res) => {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(new Blob([res]));
      link.download = record.originalName;
      // link.setAttribute('download', '');
      link.click();
    });
  }
  function handleOk() {
    emit('success', selectRows.value);
    closeModal();
  }
</script>

<style scoped></style>
