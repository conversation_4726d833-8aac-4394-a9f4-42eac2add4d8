<template>
  <BasicModal
    width="800px"
    :title="'上传'"
    :okText="'保存'"
    v-bind="$attrs"
    @register="register"
    @ok="handleOk"
    :closeFunc="handleCloseFunc"
    :maskClosable="false"
    :keyboard="false"
    class="upload-modal"
    :okButtonProps="getOkButtonProps"
    :cancelButtonProps="{ disabled: isUploadingRef }"
  >
    <div>
      <div class="upload-modal-toolbar">
        <Alert :message="getHelpText" type="info" banner class="upload-modal-toolbar__text" />
        <div class="upload-modal-toolbar__btn">
          <Dropdown v-if="importType.length > 1">
            <template #overlay>
              <Menu>
                <Menu.Item key="1" v-if="importType.includes('local')">
                  <Upload
                    :accept="getStringAccept"
                    :multiple="multiple"
                    :before-upload="beforeUpload"
                    v-model:file-list="fileList"
                    :show-upload-list="false"
                  >
                    本地导入
                  </Upload>
                </Menu.Item>
                <Menu.Item v-if="importType.includes('uploaded')" key="2" @click="() => handleSelectFile(true)"
                  >已上传导入</Menu.Item
                >
                <Menu.Item v-if="importType.includes('warehouse')" key="3" @click="() => handleSelectFile(false)"
                  >公共仓库导入</Menu.Item
                >
              </Menu>
            </template>
            <Button type="primary"> 选择文件 </Button>
          </Dropdown>
        </div>

        <Upload
          :accept="getStringAccept"
          :multiple="multiple"
          :before-upload="beforeUpload"
          v-model:file-list="fileList"
          :show-upload-list="false"
          class="upload-modal-toolbar__btn"
          v-if="importType.length === 1 && importType.includes('local')"
        >
          <Button type="primary"> 选择文件 </Button>
        </Upload>
        <Button
          type="primary"
          v-if="importType.length === 1 && importType.includes('uploaded')"
          @click="() => handleSelectFile(true)"
        >
          已上传导入
        </Button>
        <Button
          type="primary"
          v-if="importType.length === 1 && importType.includes('warehouse')"
          @click="() => handleSelectFile(false)"
        >
          公共仓库导入
        </Button>
      </div>
      <FileList v-model:dataSource="fileListRef" :columns="columns" :actionColumn="actionColumn" />
      <ImportFileModal @register="registerModal" @success="handleSuccess"></ImportFileModal>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, toRefs, unref, computed } from 'vue';
  import { Upload, Alert, Dropdown, Menu, Button } from 'ant-design-vue';
  import { BasicModal, useModal, useModalInner } from '@/components/Modal';
  // hooks
  import { useUploadType } from '../hooks/useUpload';
  import { useMessage } from '@/hooks/web/useMessage';
  // types
  import { FileItem, UploadResultStatus } from '../types/basictyping';
  import { basicUploadProps } from '../props';
  import { createTableColumns, createActionColumn } from './data';
  // utils
  import { checkImgType, isImgTypeByName, getBase64WithFile, validateFileType, getExtensionByAccept } from '../helper';
  import { ossUpload } from '@/api/main/system';
  import { buildUUID } from '@/utils/uuid';
  import { isFunction } from '@/utils/is';
  import FileList from './FileList.vue';
  import ImportFileModal from './ImportFileModal.vue';

  const props = defineProps(basicUploadProps);
  const emit = defineEmits(['change', 'register', 'delete']);
  const columns = createTableColumns();
  const actionColumn = createActionColumn(handleRemove);

  // 是否正在上传
  const isUploadingRef = ref(false);
  const fileCount = ref(0); // 用于计数选择文件数量
  const fileList = ref([]);
  const fileListRef = ref<FileItem[]>([]);
  const { accept, helpText, maxNumber, maxSize, hasDefaultAccept } = toRefs(props);
  const { createMessage } = useMessage();
  const [register, { closeModal }] = useModalInner();
  const [registerModal, { openModal }] = useModal();
  const { getStringAccept, getHelpText, getAccept } = useUploadType({
    acceptRef: accept,
    helpTextRef: helpText,
    maxNumberRef: maxNumber,
    maxSizeRef: maxSize,
    hasDefaultAcceptRef: hasDefaultAccept,
  });

  const getFileCount = computed(() => {
    return (props.previewFileList?.length ?? 0) + (fileListRef.value.length ?? 0);
  });

  const getIsSelectFile = computed(() => {
    return (
      fileListRef.value.length > 0 && !fileListRef.value.every((item) => item.status === UploadResultStatus.SUCCESS)
    );
  });

  const getOkButtonProps = computed(() => {
    const someSuccess = fileListRef.value.some((item) => item.status === UploadResultStatus.SUCCESS);
    return {
      disabled: isUploadingRef.value || fileListRef.value.length === 0 || !someSuccess,
    };
  });

  const getUploadBtnText = computed(() => {
    const someError = fileListRef.value.some((item) => item.status === UploadResultStatus.ERROR);
    return isUploadingRef.value ? '上传ing' : someError ? '重新上传失败文件' : '开始上传';
  });

  const getFileSuffixs = computed(() => {
    return getExtensionByAccept(unref(getAccept));
  });

  function handleSuccess(rows) {
    const fileList = rows.map((item) => {
      return {
        uuid: item.ossId,
        name: item.originalName,
        percent: 100,
        type: item.originalName.split('.').pop(),
        status: UploadResultStatus.SUCCESS,
        thumbUrl: isImgTypeByName(item.originalName) ? item.thumbUrl || item.url : '',
        url: item.url,
        response: item,
      };
    });
    fileListRef.value = [...fileListRef.value, ...fileList];
  }

  function handleSelectFile(isMe: boolean) {
    openModal(true, { isMe, contentType: unref(getFileSuffixs) });
  }
  // 上传前校验
  function beforeUpload(file: File) {
    const { size, name } = file;
    const { maxSize, maxNumber } = props;

    // 设置最大值，则判断
    if (maxSize && file.size / 1024 / 1024 >= maxSize) {
      createMessage.error(`只能上传不超过${maxSize}MB的文件!`);
      return false;
    }

    if (!validateFileType(file, unref(getAccept))) {
      createMessage.error(`文件格式不正确, 请上传${unref(getAccept).join('、')}格式文件!`);
      return false;
    }

    fileCount.value++;
    if (maxNumber && unref(fileCount) + unref(getFileCount) > maxNumber) {
      createMessage.error(`最多只能上传${props.maxNumber}个文件!`);
      return false;
    }
    const commonItem = {
      uuid: buildUUID(),
      file,
      size,
      name,
      percent: 0,
      type: name.split('.').pop(),
    };
    // 生成图片缩略图
    if (checkImgType(file)) {
      // beforeUpload，如果异步会调用自带上传方法
      // file.thumbUrl = await getBase64(file);
      getBase64WithFile(file).then(({ result: thumbUrl }) => {
        fileListRef.value = [
          ...unref(fileListRef),
          {
            thumbUrl,
            ...commonItem,
          },
        ];
        uploadApiByItem({
          thumbUrl,
          ...commonItem,
        });
      });
    } else {
      fileListRef.value = [...unref(fileListRef), commonItem];
      uploadApiByItem(commonItem);
    }
    return false;
  }

  // 删除
  function handleRemove(record: FileItem) {
    const field = 'uuid';
    const index = fileListRef.value.findIndex((item) => item[field] === record[field]);
    index !== -1 && fileListRef.value.splice(index, 1);
    if (unref(fileCount) > 0) fileCount.value -= 1;
    emit('delete', record);
  }

  async function uploadApiByItem(item: FileItem) {
    const { api } = props;
    const apiFun = isFunction(api) ? api : ossUpload;
    try {
      item.status = UploadResultStatus.UPLOADING;
      const ret = await apiFun(
        {
          file: item.file,
        },
        function onUploadProgress(progressEvent: ProgressEvent) {
          const complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0;
          item.percent = complete;
        },
      );

      const { data } = ret;
      if (!data) {
        throw new Error('上传失败');
      }
      item.status = UploadResultStatus.SUCCESS;
      item.response = data;

      const index = fileListRef.value.findIndex((i) => i.uuid === item.uuid);
      const list = [...fileListRef.value];
      list[index] = item;
      fileListRef.value = [...list];

      if (unref(fileCount) > 0) fileCount.value -= 1;
      return {
        success: true,
        error: null,
      };
    } catch (e) {
      console.log(e);
      item.status = UploadResultStatus.ERROR;
      return {
        success: false,
        error: e,
      };
    }
  }
  //   点击保存
  function handleOk() {
    const { maxNumber } = props;

    if (maxNumber && unref(getFileCount) > maxNumber) {
      return createMessage.error(`最多只能上传${props.maxNumber}个文件!`);
    }

    if (isUploadingRef.value) {
      return createMessage.warning('保存Warn');
    }

    const fileList: Recordable[] = unref(fileListRef)
      .filter((item) => item.status === UploadResultStatus.SUCCESS && item.response)
      .map((item) => item.response!);

    // 存在一个上传成功的即可保存
    if (fileList.length <= 0) {
      return createMessage.warning('保存Error');
    }
    fileListRef.value = [];
    closeModal();
    fileCount.value = 0;
    emit('change', fileList);
  }

  // 点击关闭：则所有操作不保存，包括上传的
  async function handleCloseFunc() {
    if (!isUploadingRef.value) {
      fileListRef.value = [];
      fileCount.value = 0;
      return true;
    } else {
      createMessage.warning('上传Wait');
      return false;
    }
  }
</script>
<style lang="less">
  .upload-modal {
    .ant-upload-list {
      display: none;
    }

    .ant-table-wrapper .ant-spin-nested-loading {
      padding: 0;
    }

    &-toolbar {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &__btn {
        flex: 1;
        margin-left: 8px;
        text-align: right;
      }
    }
  }
</style>
