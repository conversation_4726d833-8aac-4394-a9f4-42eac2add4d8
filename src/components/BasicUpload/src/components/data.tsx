import type { BasicColumn, ActionItem } from '@/components/Table';
import { FileBasicColumn, FileItem, PreviewFileItem, UploadResultStatus } from '../types/basictyping';
import { Progress, Tag } from 'ant-design-vue';
import TableAction from '@/components/Table/src/components/TableAction.vue';
import Thumbnail from './Thumbnail.vue';

// 文件上传列表
export function createTableColumns(): FileBasicColumn[] {
  return [
    {
      dataIndex: 'thumbUrl',
      title: '略缩图',
      width: 100,
      customRender: ({ record }) => {
        const { thumbUrl, response } = (record as FileItem) || {};
        return <Thumbnail fileUrl={thumbUrl || response?.thumbnail || response?.url} fileName={response?.fileSuffix} />;
      },
    },
    {
      dataIndex: 'name',
      title: '文件名',
      align: 'left',
      customRender: ({ text, record }) => {
        const { percent = 100, status: uploadStatus } = (record as FileItem) || {};
        let status: 'normal' | 'exception' | 'active' | 'success' = 'normal';
        if (uploadStatus === UploadResultStatus.ERROR) {
          status = 'exception';
        } else if (uploadStatus === UploadResultStatus.UPLOADING) {
          status = 'active';
        } else if (uploadStatus === UploadResultStatus.SUCCESS) {
          status = 'success';
        }
        return (
          <div>
            <p class="truncate mb-1 max-w-[280px]" title={text}>
              {text}
            </p>
            <Progress percent={status === 'success' ? 100 : percent} size="small" status={status} />
          </div>
        );
      },
    },
    {
      dataIndex: 'size',
      title: '文件大小',
      width: 100,
      customRender: ({ text = 0, record }) => {
        if (text) {
          return text && (text / 1024).toFixed(2) + 'KB';
        } else {
          return record?.response?.fileReadSize || 0;
        }
      },
    },
    {
      dataIndex: 'status',
      title: '状态',
      width: 100,
      customRender: ({ text }) => {
        if (text === UploadResultStatus.SUCCESS) {
          return <Tag color="green">{() => '上传Success'}</Tag>;
        } else if (text === UploadResultStatus.ERROR) {
          return <Tag color="red">{() => '上传Error'}</Tag>;
        } else if (text === UploadResultStatus.UPLOADING) {
          return <Tag color="blue">{() => '上传ing'}</Tag>;
        }

        return text || '待上传';
      },
    },
  ];
}
export function createActionColumn(handleRemove: Function): FileBasicColumn {
  return {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: false,
    customRender: ({ record }) => {
      const actions: ActionItem[] = [
        {
          label: '删除',
          color: 'error',
          onClick: handleRemove.bind(null, record),
        },
      ];
      return <TableAction actions={actions} outside={true} class="!inline-flex" />;
    },
  };
}
// 文件预览列表
export function createPreviewColumns(): BasicColumn[] {
  return [
    {
      dataIndex: 'url',
      title: '略缩图',
      width: 100,
      customRender: ({ record }) => {
        const { url, thumbnail, fileSuffix } = (record as PreviewFileItem) || {};
        return <Thumbnail fileUrl={thumbnail || url} previewUrl={url} fileName={fileSuffix} />;
      },
    },
    {
      dataIndex: 'originalName',
      title: '文件名',
      align: 'left',
    },
    {
      dataIndex: 'fileReadSize',
      title: '文件大小',
      width: 100,
    },
  ];
}

export function createPreviewActionColumn({
  handleRemove,
  handleDownload,
  disabled,
}: {
  handleRemove: Fn;
  handleDownload: Fn;
  disabled?: boolean;
}): FileBasicColumn {
  return {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    fixed: false,
    customRender: ({ record }) => {
      const actions: ActionItem[] = [
        {
          label: '删除',
          color: 'error',
          disabled: disabled,
          onClick: handleRemove.bind(null, record),
        },
        {
          label: '下载',
          onClick: handleDownload.bind(null, record),
        },
      ];

      return <TableAction actions={actions} outside={true} class="!inline-flex" />;
    },
  };
}
