<template>
  <BasicUpload v-bind="props" :value="newValue" :oss-list="ossList" @change="handleChange"> </BasicUpload>
</template>

<script setup lang="ts">
  import { ref, unref, watch } from 'vue';
  import { Form } from 'ant-design-vue';
  import { formUploadProps } from './props';
  import { getOssListByIds } from '@/api/common';
  import { isArray, isEmpty, isString } from '@/utils/is';
  import BasicUpload from './BasicUpload.vue';

  const props = defineProps(formUploadProps);
  const ossList = ref<Recordable[]>([]);
  const newValue = ref<Array<string>>([]);
  const isInnerOperate = ref<boolean>(false);
  const formItemContext = Form.useInjectFormItemContext();
  const emit = defineEmits(['update:value', 'change']);
  function handleChange(fileList, ossList) {
    const value = props.serialization ? fileList.join(',') : fileList;
    emit('update:value', value);
    emit('change', value, ossList);
    isInnerOperate.value = true;
    formItemContext?.onFieldChange();
  }
  watch(
    () => props.value,
    (value) => {
      if (unref(isInnerOperate)) {
        isInnerOperate.value = false;
        return;
      }

      if (isEmpty(value)) {
        ossList.value = [];
        newValue.value = [];
        return;
      }

      if (isArray(value)) {
        newValue.value = value;
      }

      if (props.serialization && isString(value)) {
        newValue.value = value?.split(',') || [];
      }

      getOssListByIds(unref(newValue)).then((res) => {
        ossList.value = res;
      });
    },
    { immediate: true },
  );
</script>

<style scoped></style>
