import type { PropType } from 'vue';
import { FileBasicColumn } from './types/basictyping';

import type { Options } from 'sortablejs';

import { Merge } from '@/utils/types';
import { propTypes } from '@/utils/propTypes';
import { BasicColumn } from '@/components/Table';

type SortableOptions = Merge<
  Omit<Options, 'onEnd'>,
  {
    onAfterEnd?: <T = any, R = any>(params: T) => R;
    // ...可扩展
  }
>;

//暂定两个props
const uploadProps = {
  value: {
    type: Array as PropType<Array<string | number>>,
    default: () => [],
  },
  // 上传类型
  ossList: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  previewFileList: {
    type: Array as PropType<Recordable[]>,
    default: () => [],
  },
  importType: {
    type: Array as PropType<Array<'local' | 'warehouse' | 'uploaded'>>,
    default: () => ['local'],
  },
  disabled: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  //0表示不限制
  maxNumber: {
    type: Number as PropType<number>,
    default: 0,
  },
  // 文件最大多少MB //0表示不限制
  maxSize: {
    type: Number as PropType<number>,
    default: 0,
  },
  // 根据后缀，或者其他
  accept: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  // 是否默认的accept
  hasDefaultAccept: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  helpText: {
    type: String as PropType<string>,
    default: '',
  },
  api: {
    type: Function as PropType<PromiseFn>,
    default: null,
    // required: true,
  },
  multiple: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
};
export const basicUploadProps = {
  ...uploadProps,
};
export const formUploadProps = {
  ...basicUploadProps,
  value: {
    type: [String, Array] as PropType<string | string[]>,
  },
  // 是否序列化成字符串
  serialization: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
};
export const fileListProps = {
  columns: {
    type: Array as PropType<BasicColumn[] | FileBasicColumn[]>,
    default: null,
  },
  actionColumn: {
    type: Object as PropType<FileBasicColumn>,
    default: null,
  },
  dataSource: {
    type: Array as PropType<any[]>,
    default: null,
  },
  openDrag: {
    type: Boolean,
    default: false,
  },
  dragOptions: {
    type: Object as PropType<SortableOptions>,
    default: () => ({}),
  },
};
