import { uniq } from 'lodash-es';
import { UploadFileType } from './types/basictyping';

export function checkFileType(file: File, accepts: string[]) {
  const newTypes = accepts.join('|');
  // const reg = /\.(jpg|jpeg|png|gif|txt|doc|docx|xls|xlsx|xml)$/i;
  const reg = new RegExp('\\.(' + newTypes + ')$', 'i');

  return reg.test(file.name);
}

export function checkImgType(file: File) {
  return isImgTypeByName(file.name);
}

export function isImgTypeByName(name: string) {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(name);
}

export function getBase64WithFile(file: File) {
  return new Promise<{
    result: string;
    file: File;
  }>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve({ result: reader.result as string, file });
    reader.onerror = (error) => reject(error);
  });
}

export function getFileType(extension: string): string {
  const normalizedExt = extension.toLowerCase().trim();

  // 使用正则表达式映射来合并相同类型的文件扩展名
  const FILE_TYPE_GROUPS: { [regex: string]: string } = {
    '(pdf)$': 'Pdf',
    '(doc|docx)$': 'Word',
    '(xls|xlsx)$': 'Excel',
    '(ppt|pptx)$': 'Ppt',
    '(txt|text)$': 'Text',
    '(md|markdown)$': 'Markdown',
    '(jpg|jpeg|png|gif|webp)$': 'Image',
    '(rar|zip|7z|gz|tar)$': 'Zip',
    '(mp4|avi|mov|mkv)$': 'Video',
    '(mp3|wav|aac)$': 'Audio',
  };

  // 遍历正则表达式映射，查找匹配项
  for (const regex in FILE_TYPE_GROUPS) {
    if (new RegExp(regex, 'i').test(normalizedExt)) {
      return FILE_TYPE_GROUPS[regex];
    }
  }

  // 如果没有找到匹配项，则返回未知文件类型
  return 'Unknown';
}

/**
 * 文件类型校验函数
 * @param file 文件对象
 * @param acceptedTypes 接受的文件类型数组，支持通配符如'image/*'
 * @returns 是否通过校验
 */
export function validateFileType(file: File, acceptedTypes: readonly string[]): boolean {
  if (acceptedTypes.length === 0) return true;
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  // 遍历接受的文件类型列表
  return acceptedTypes.some((type) => {
    if (type.endsWith('*')) {
      // 处理通配符，如'image/*'，匹配类型前缀
      const prefix = type.slice(0, type.length - 1);
      return fileType.startsWith(prefix);
    } else {
      // 精确匹配MIME类型或文件扩展名
      return fileType === type || fileName.endsWith('.' + type.replace('.', ''));
    }
  });
}

/**
 * 根据接受的类型数组，获取对应的文件扩展名数组。
 *
 * 此函数用于处理一个接受多种文件类型的数组，并返回一个包含所有可能的文件扩展名的数组。
 * 它处理了通配符类型（如"*image/*"）和具体文件类型（如"image/png"）。
 *
 * @param acceptedTypes 接受的文件类型数组。
 * @returns 返回一个数组，包含所有可能的文件扩展名。
 */
export function getExtensionByAccept(acceptedTypes: string[]): string[] {
  // 如果接受的类型数组为空或不存在，则直接返回空数组。
  if (!acceptedTypes || acceptedTypes.length === 0) return [];

  // 处理接受的类型数组。
  const fileTypes = acceptedTypes.reduce((prev, cur) => {
    // 判断当前类型是否为通配符类型，如果是，则只取类型的第一部分（如"*/*"取"*"）。
    // 如果不是通配符类型，则通过getFileType函数获取具体的文件类型。
    const curFileType = cur.endsWith('*') ? cur.split('/')[0] : getFileType(cur);
    // 将获取到的文件类型转换为上传文件时使用的文件类型枚举值。
    // 如果文件类型不存在于枚举中，则返回空数组。
    const convertFileType = UploadFileType[curFileType.toUpperCase()];
    return prev.concat(convertFileType ? convertFileType.split('|') : []);
  }, []);

  // 去除可能存在的重复文件扩展名，返回最终的结果数组。
  return uniq(fileTypes);
}
