<!--
 * @Author: wang<PERSON>i
 * @Date: 2024-04-19 08:47:07
 * @LastEditors: wangfei
 * @LastEditTime: 2024-05-28 13:38:39
 * @Descripttion: 用户选择组件
-->
<template>
  <div class="user-picker">
    <div class="user-picker-select">
      <Select
        :value="userIds"
        :open="false"
        :loading="loading"
        :options="userInfos"
        :placeholder="`请${title}`"
        :disabled="isDisabled ? true : false"
        :mode="multiple ? 'multiple' : undefined"
        :fieldNames="{ label: 'roomNumber', value: 'id' }"
        @change="handleMultipleUserChange($event, multiple)"
        @click="() => handleOpenModal()"
      />
    </div>
    <HomePickerModal v-bind="bindProps" @register="registerModal" @change="handleChange" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, unref, computed, useAttrs, watch } from 'vue';
  import { Form, AvatarGroup, Avatar, AvatarProps, Tooltip, Select } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useModal } from '@/components/Modal';
  import HomePickerModal from './components/homePickerModal.vue';
  import { isArray, isString } from '@/utils/is';
  import { basicProps } from './props';
  import { isNumber, pick } from 'lodash-es';
  import { getUserInfos } from '@/api/common';
  import { getHouseBasicInfoList } from '@/api/gaoqian/houseManage';

  const props = defineProps({
    ...basicProps,
    maxCount: {
      type: Number as PropType<number>,
      default: 1,
    },
    size: {
      type: String as PropType<AvatarProps['size']>,
      default: 'large',
    },
    shape: {
      type: String as PropType<'circle' | 'square'>,
      default: 'square',
    },
    mode: {
      type: String as PropType<'select' | 'avatar' | 'detail'>,
      default: 'select',
    },
    title: {
      type: String as PropType<string>,
      default: '选择房屋',
    },
  });
  const emit = defineEmits(['change', 'update:value']);
  const formItemContext = Form.useInjectFormItemContext();
  const [registerModal, { openModal }] = useModal();
  const attrs = useAttrs();
  const loading = ref<boolean>(false);
  const userInfos = ref<Recordable[]>([]);
  const isInnerOperate = ref<boolean>(false);

  watch(
    () => props.value,
    (value: any) => {
      if (value) {
        initUserInfos(value);
      } else {
        userInfos.value = [];
      }
    },
    { immediate: true },
  );

  const userIds = computed(() => {
    return unref(userInfos).map((item) => item.id);
  });

  const isDisabled = computed(() => {
    return attrs?.disabled || false;
  });

  const bindProps = computed(() => {
    return pick({ ...attrs, ...props }, ['title', 'multiple', 'isNotDataAuth', 'otherParams']);
  });

  const handleMultipleUserChange = (value, multiple) => {
    if (multiple) {
      const users = userInfos.value.filter((item) => value.includes(item.id));
      handleChange(users);
    }
  };

  const handleOpenModal = () => {
    if (isDisabled.value) return;
    openModal(true, {
      userInfos: unref(userInfos),
    });
  };

  function handleChange(users: any[]) {
    const userKeys = users.map((item) => item.id);
    const value = props.serialization ? userKeys.join(',') : userKeys;
    emit('change', value, users);
    emit('update:value', value);
    userInfos.value = users;
    isInnerOperate.value = true;
    formItemContext?.onFieldChange();
  }

  function initUserInfos(value) {
    let userIds: any[] = [];
    if (isArray(value)) {
      userIds = value;
    } else if (isString(value)) {
      if (props.serialization) {
        userIds = value.split(',');
      } else {
        userIds = [value];
      }
    } else if (isNumber(value)) {
      userIds = [value];
    } else {
      userIds = [];
    }

    if (unref(isInnerOperate)) {
      isInnerOperate.value = false;
      return;
    }
    loading.value = true;
    if (!userIds || userIds.length === 0) return;
    getHouseBasicInfoList({ params: { ids: userIds } }).then((res) => {
      userInfos.value = res.rows.map((item) => {
        return {
          ...item,
        };
      });
      loading.value = false;
      isInnerOperate.value = true;
    });
  }

  function beautySub(str, len) {
    var reg = /[\u4e00-\u9fa5]/g,
      slice = str.substring(0, len),
      cCharNum = ~~(slice.match(reg) && slice.match(reg).length),
      realen = slice.length * 2 - cCharNum - 1;
    return str.substr(0, realen) + (realen < str.length ? '...' : '');
  }
</script>

<style lang="less" scoped>
  .avatar {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &-name {
      margin-top: 2px;
      color: var(--text-color);
      font-size: 12px;
    }

    &-btn {
      border: 1px dashed var(--border-color);
      background-color: var(--custom-bg-color);
      color: var(--text-color);
    }
  }

  .ant-avatar-group > :not(:first-child) {
    margin-inline-start: 5px;
  }

  .ant-popover-inner-content > .avatar:not(:first-child) {
    margin-inline-start: 5px;
  }
</style>
