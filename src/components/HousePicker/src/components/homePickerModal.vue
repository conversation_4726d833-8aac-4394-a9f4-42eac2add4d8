<template>
  <BasicModal
    width="42rem"
    v-bind="$attrs"
    :title="title"
    :canFullscreen="false"
    :destroyOnClose="true"
    :getContainer="getContainer"
    @register="registerModal"
    @ok="handleConfirm"
    @open-change="handleOpenChange"
  >
    <Card v-show="isLoading" :bodyStyle="{ padding: 0 }">
      <div class="user-picker flex">
        <HomeTree
          class="user-picker-tree"
          :sourceData="houseDatas"
          :defaultExpandLevel="2"
          @select="handleHouseSelect"
        />

        <UserList
          :value="selectUsers"
          :houseId="houseId"
          :multiple="multiple"
          :isNotDataAuth="isNotDataAuth"
          :otherParams="otherParams"
          @selected="handleSelected"
        />
      </div>

      <div class="user-picker-selected">
        <span class="user-picker-selected-title" style="">已选择：</span>
        <Space style="margin: 0" :size="[0, 4]" wrap>
          <Tooltip placement="topLeft" :title="item?.dept?.deptName" :key="item.id" v-for="item in selectUsers">
            <Tag closable :bordered="false" @close="handleCloseTag(item.id)">{{ item.roomNumber }}</Tag>
          </Tooltip>
        </Space>
      </div>
    </Card>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, unref, nextTick } from 'vue';
  import { Card, Space, Tag, Tooltip } from 'ant-design-vue';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import HomeTree from './homeTree.vue';
  import UserList from './userList.vue';
  import { basicProps } from '../props';
  import { pick } from 'lodash-es';
  import { getCourtyardInfoTreeList } from '@/api/gaoqian/houseManage';

  const props = defineProps({
    ...pick(basicProps, ['multiple', 'isNotDataAuth', 'otherParams']),
    title: {
      type: String as PropType<string>,
    },
  });

  const emit = defineEmits(['register', 'change']);
  const houseId = ref();
  const isLoading = ref(false);
  const houseDatas = ref<Recordable[]>([]);
  const selectUsers = ref<Recordable[]>([]);

  const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
    setModalProps({
      loading: true,
    });

    isLoading.value = false;
    selectUsers.value = data?.userInfos;
    houseDatas.value = await getCourtyardInfoTreeList();
    await nextTick();
    setModalProps({
      loading: false,
    });
    isLoading.value = true;
  });

  const getContainer = () => {
    return document.body;
  };

  const handleConfirm = () => {
    emit('change', unref(selectUsers));
    closeModal();
  };

  const handleHouseSelect = (id: number | string) => {
    houseId.value = id;
  };

  const handleSelected = (data: Recordable<any>[]) => {
    selectUsers.value = data;
  };

  // 处理关闭后打开导致列表数据问题
  const handleOpenChange = (visible: boolean) => {
    if (!visible) {
      houseId.value = '';
    }
  };

  const handleCloseTag = (record: string | number) => {
    const users = unref(selectUsers)?.filter((item) => item.id != record);
    selectUsers.value = users;
  };
</script>

<style lang="less" scoped>
  .user-picker {
    &-tree {
      width: 45%;
      height: 360px;
      border-right: 1px solid var(--border-color);

      :deep(.vben-tree) {
        background-color: transparent;
      }
    }

    &-user {
      display: flex;
      flex-direction: column;
      width: 55%;
      height: 360px;
    }

    &-selected {
      display: flex;
      padding: 8px;
      border-top: 1px solid var(--border-color);

      &-title {
        flex-shrink: 0;
        line-height: 24px;
      }
    }
  }
</style>
