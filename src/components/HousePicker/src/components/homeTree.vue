<!--
 * @Author: wang<PERSON>i
 * @Date: 2024-04-16 16:29:15
 * @LastEditors: wangfei
 * @LastEditTime: 2024-05-16 10:02:46
 * @Descripttion: 部门选择
-->
<template>
  <div class="overflow-hidden border-gray-200 border-r">
    <BasicTree
      v-if="treeData.length"
      v-bind="$attrs"
      treeWrapperClassName="h-full overflow-auto"
      :treeData="treeData"
      :fieldNames="{ key: 'parentId', title: 'name' }"
      @select="handleSelect"
    />
  </div>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import { BasicTree, TreeItem } from '@/components/Tree';
  const props = defineProps({
    sourceData: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const emit = defineEmits(['select']);

  const transformTreeData = (data: Recordable[], parentId?: string) => {
    return data.map((item) => {
      const realId = item.id.split('-')[0];
      const newId = parentId ? `${parentId}~${realId}` : `${realId}`;

      return {
        ...item,
        parentId: newId,
        children: item.type === 'Floor' ? undefined : transformTreeData(item.children, newId),
      };
    });
  };
  const treeData = computed(() => {
    return transformTreeData(props.sourceData) as unknown as TreeItem[];
  });

  function handleSelect(keys: number[]) {
    console.log(keys);

    emit('select', keys[0]);
  }
</script>
