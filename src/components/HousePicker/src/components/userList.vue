<template>
  <div class="user-picker-user">
    <div class="search">
      <InputSearch
        :status="undefined"
        v-model:value="roomNumber"
        placeholder="请输入房屋编号"
        allow-clear
        @search="onSearch"
      />
    </div>
    <n-infinite-scroll ref="infiniteScrollRef" class="lists" :distance="10" @load="onScrollLoad">
      <template v-if="multiple">
        <List class="w-full" size="small">
          <List.Item v-for="item in userList" :key="item.id">
            <div class="flex items-center">
              <span class="ml-2">{{ `${item.unit}-${item.roomNumber}` }}</span>
            </div>
            <template #actions>
              <Checkbox @change="(e) => handleCheckboxChange(item.id, e.target.checked)" :checked="isCheck(item)" />
            </template>
          </List.Item>
        </List>
      </template>

      <template v-else>
        <RadioGroup class="w-full" :value="userValue.map((item) => item.id)?.[0]" @change="handleRadioChange">
          <List class="w-full" size="small">
            <List.Item v-for="item in userList" :key="item.id">
              <div class="flex items-center">
                <span class="ml-2">{{ `${item.unit}-${item.roomNumber}` }}</span>
              </div>
              <template #actions>
                <Radio :value="item.id" />
              </template>
            </List.Item>
          </List>
        </RadioGroup>
      </template>
    </n-infinite-scroll>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, unref, computed } from 'vue';
  import { Checkbox, RadioGroup, Radio, List, Avatar, InputSearch } from 'ant-design-vue';
  import { NInfiniteScroll } from 'naive-ui';
  import { getUserList } from '@/api/common';
  import { basicProps } from '../props';
  import { cloneDeep, pick } from 'lodash-es';
  import { getHouseBasicInfoList } from '@/api/gaoqian/houseManage';
  const props = defineProps({
    value: {
      type: Array as PropType<Recordable[]>,
      default: () => [],
    },
    houseId: {
      type: String,
    },
    ...pick(basicProps, ['multiple', 'isNotDataAuth', 'otherParams']),
  });
  const emit = defineEmits(['selected']);
  const hasMore = ref(true);
  const loading = ref(false);
  const curPageNum = ref(1);
  const roomNumber = ref('');
  const userList = ref<Recordable[]>([]);
  const infiniteScrollRef = ref<InstanceType<typeof NInfiniteScroll>>();
  const userValue = computed<Recordable[]>(() => props.value);

  function isCheck(item) {
    return unref(userValue).filter((user) => user.id === item.id).length > 0;
  }

  const handleRadioChange = (e) => {
    const { value } = e.target;
    const user = userList.value.find((item) => item.id == value);
    emit('selected', [user]);
  };

  const handleCheckboxChange = (value, checked) => {
    const list = cloneDeep(props.value);
    if (checked) {
      const user = userList.value.find((item) => item.id == value);
      const users = [...list, user];
      emit('selected', users);
    } else {
      const users = list.filter((item) => value != item.id);
      emit('selected', users);
    }
  };

  const onSearch = () => {
    getHomeLists();
  };

  const onScrollLoad = () => {
    if (unref(hasMore) && !unref(loading)) {
      curPageNum.value++;
      getHomeLists(unref(curPageNum));
    }
  };

  const getHomeLists = async (pageNum = 1, pageSize = 10) => {
    if (unref(loading)) return;
    loading.value = true;
    const ids = props.houseId?.split('~');
    const params = {
      buildId: ids?.[1] && parseInt(ids[1]),
      courtyardId: ids?.[0] && parseInt(ids[0]),
      floor: ids?.[2] && parseInt(ids[2].replace('B', '-')),
    };
    const { rows, total } = await getHouseBasicInfoList({
      pageNum,
      pageSize,
      roomNumber: unref(roomNumber),
      ...props.otherParams,
      ...params,
    });

    const users = rows.map((item) => {
      return {
        ...item,
      };
    });

    if (pageNum === 1) {
      curPageNum.value = 1;
      unref(infiniteScrollRef)?.scrollbarInstRef?.scrollTo({ top: 0 });
      userList.value = [].concat(users);
    } else {
      userList.value = userList.value.concat(users);
    }

    hasMore.value = total > userList.value.length;
    loading.value = false;
  };

  watch(
    () => props.houseId,
    () => {
      getHomeLists();
    },
    { immediate: true },
  );
</script>

<style lang="less" scoped>
  .user-picker-user {
    .search {
      flex-shrink: 0;
      padding: 0.5rem;
      border-bottom: 1px solid var(--border-color);

      :deep(.ant-input-affix-wrapper) {
        border-color: var(--custom-border-color) !important;
      }
    }

    .lists {
      flex: 1;
    }
  }
</style>
