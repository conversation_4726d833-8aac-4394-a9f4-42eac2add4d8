import type { PropType } from 'vue';

export const basicProps = {
  value: {
    type: [String, Number, Array] as PropType<string | number | string[]>,
  },
  multiple: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  serialization: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  isNotDataAuth: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  // 其他参数
  otherParams: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
};
