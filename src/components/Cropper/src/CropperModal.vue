<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :title="'头像上传'"
    width="800px"
    :canFullscreen="false"
    @ok="handleOk"
    :okText="'确认并上传'"
  >
    <div :class="prefixCls">
      <div :class="`${prefixCls}-left`">
        <div :class="`${prefixCls}-cropper`">
          <CropperImage
            v-if="src"
            :src="src"
            height="300px"
            :circled="circled"
            @cropend="handleCropend"
            @ready="handleReady"
          />
        </div>

        <div :class="`${prefixCls}-toolbar`">
          <Upload :fileList="[]" accept="image/*" :beforeUpload="handleBeforeUpload">
            <Tooltip :title="'选择图片'" placement="bottom">
              <Button size="small" type="primary">
                <Icon icon="ant-design:upload-outlined" #icon />
              </Button>
            </Tooltip>
          </Upload>
          <Space>
            <Tooltip :title="'重置'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('reset')">
                <Icon icon="ant-design:reload-outlined" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'逆时针旋转'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('rotate', -45)">
                <Icon icon="ant-design:rotate-left-outlined" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'顺时针旋转'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('rotate', 45)">
                <Icon icon="ant-design:rotate-right-outlined" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'水平翻转'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('scaleX')">
                <Icon icon="vaadin:arrows-long-h" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'垂直翻转'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('scaleY')">
                <Icon icon="vaadin:arrows-long-v" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'放大'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('zoom', 0.1)">
                <Icon icon="ant-design:zoom-in-outlined" #icon />
              </Button>
            </Tooltip>
            <Tooltip :title="'缩小'" placement="bottom">
              <Button type="primary" size="small" :disabled="!src" @click="handlerToolbar('zoom', -0.1)">
                <Icon icon="ant-design:zoom-out-outlined" #icon />
              </Button>
            </Tooltip>
          </Space>
        </div>
      </div>
      <div :class="`${prefixCls}-right`">
        <div :class="`${prefixCls}-preview`">
          <img :src="previewSource" v-if="previewSource" :alt="'预览'" />
        </div>
        <template v-if="previewSource">
          <div :class="`${prefixCls}-group`">
            <Avatar :src="previewSource" size="large" />
            <Avatar :src="previewSource" :size="48" />
            <Avatar :src="previewSource" :size="64" />
            <Avatar :src="previewSource" :size="80" />
          </div>
        </template>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, PropType } from 'vue';
  import CropperImage from './Cropper.vue';
  import type { CropendResult, Cropper } from './typing';
  import { Space, Upload, Avatar, Tooltip, Button } from 'ant-design-vue';
  import { useDesign } from '@/hooks/web/useDesign';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { dataURLtoBlob } from '@/utils/file/base64Conver';
  import Icon from '@/components/Icon/Icon.vue';
  import { isFunction } from '@/utils/is';

  type apiFunParams = { file: File };

  defineOptions({ name: 'CropperModal' });

  const props = defineProps({
    circled: { type: Boolean, default: true },
    uploadApi: {
      type: Function as PropType<(params: apiFunParams) => Promise<any>>,
    },
    src: { type: String },
    size: { type: Number },
  });

  const emit = defineEmits(['uploadSuccess', 'uploadError', 'register']);

  let filename = '';
  const src = ref(props.src || '');
  const previewSource = ref('');
  const cropper = ref<Cropper>();
  let scaleX = 1;
  let scaleY = 1;

  const { prefixCls } = useDesign('cropper-am');
  const [register, { closeModal, setModalProps }] = useModalInner();

  // Block upload
  function handleBeforeUpload(file: File) {
    if (props.size && file.size > 1024 * 1024 * props.size) {
      emit('uploadError', { msg: 'component.cropper.imageTooBig' });
      return false;
    }
    const reader = new FileReader();
    reader.readAsDataURL(file);
    src.value = '';
    previewSource.value = '';
    reader.onload = function (e) {
      src.value = (e.target?.result as string) ?? '';
      filename = file.name;
    };
    return false;
  }

  function handleCropend({ imgBase64 }: CropendResult) {
    previewSource.value = imgBase64;
  }

  function handleReady(cropperInstance: Cropper) {
    cropper.value = cropperInstance;
  }

  function handlerToolbar(event: string, arg?: number) {
    if (event === 'scaleX') {
      scaleX = arg = scaleX === -1 ? 1 : -1;
    }
    if (event === 'scaleY') {
      scaleY = arg = scaleY === -1 ? 1 : -1;
    }
    cropper?.value?.[event]?.(arg);
  }

  async function handleOk() {
    const uploadApi = props.uploadApi;
    if (uploadApi && isFunction(uploadApi)) {
      const blob = dataURLtoBlob(previewSource.value);
      try {
        setModalProps({ confirmLoading: true });
        // const result = await uploadApi({ name: 'file', file: blob, filename });
        const result = await uploadApi({
          file: new File([blob], filename, { type: blob.type }),
        });

        emit('uploadSuccess', { source: previewSource.value, data: result.url });
        closeModal();
      } finally {
        setModalProps({ confirmLoading: false });
      }
    }
  }
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-cropper-am';

  .@{prefix-cls} {
    display: flex;

    &-left,
    &-right {
      height: 340px;
    }

    &-left {
      width: 55%;
    }

    &-right {
      width: 45%;
    }

    &-cropper {
      height: 300px;
      background: #eee;
      background-image: linear-gradient(45deg, rgb(0 0 0 / 25%) 25%, transparent 0, transparent 75%, rgb(0 0 0 / 25%) 0),
        linear-gradient(45deg, rgb(0 0 0 / 25%) 25%, transparent 0, transparent 75%, rgb(0 0 0 / 25%) 0);
      background-position:
        0 0,
        12px 12px;
      background-size: 24px 24px;
    }

    &-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
    }

    &-preview {
      width: 220px;
      height: 220px;
      margin: 0 auto;
      overflow: hidden;
      border: 1px solid @border-color-base;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    &-group {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid @border-color-base;
    }
  }
</style>
