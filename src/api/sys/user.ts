import { ErrorMessageMode } from '#/axios';
import { defHttp } from '@/utils/http/axios';

enum Api {
  Login = '/login',
  Logout = '/logout',
  GetUserInfo = '/getInfo',
  getCode = '/captchaImage',
  getMyManage = '/system/courtyardInfo/query/myManage/Courtyard',
}

/**
 * @description: user login api
 */
export function loginApi(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function getInfo() {
  return defHttp.get({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function doLogout() {
  return defHttp.post({ url: Api.Logout, params: {} });
}
export function getCode() {
  return defHttp.get({ url: Api.getCode });
}
export function getMyManage(params?) {
  return defHttp.post({ url: Api.getMyManage, params });
}
