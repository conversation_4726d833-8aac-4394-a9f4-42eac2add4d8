import { defHttp } from '@/utils/http/axios';

enum Api {
  getConfigKey = '/system/config/configKey/',
  getDictTypes = '/system/dict/data/type/',
  getRouters = '/getRouters',
  queryDictByKeys = '/system/dict/type/queryByKeys',
}
//获取多个字段值
export const queryDictByKeys = (params) => defHttp.post({ url: Api.queryDictByKeys, params });

//获取单值参数
export const getConfigKey = (configKey) => defHttp.get({ url: Api.getConfigKey + configKey });
//获取字段值
export const getDictTypes = (dictType) => defHttp.get({ url: Api.getDictTypes + dictType });
//获取菜单
export const getRouters = () => defHttp.get({ url: Api.getRouters });
