import { defHttp } from '@/utils/http/axios';

enum Api {
  getMessageList = '/message/internalMessage/my/list',
  putReadMessageById = '/message/internalMessage/read/',
  putReadMessage = '/message/internalMessage/read',
}

// 获取我的站内消息：当前登录用户的站内消息列表
export function getMessageList(params: any) {
  return defHttp.post({ url: Api.getMessageList, params });
}
// 已读我的未读消息，指定消息
export function putReadMessageById(ids: string | number | string[]) {
  return defHttp.put({ url: Api.putReadMessageById + ids });
}
// 已读我的全部未读消息
export function putReadMessage() {
  return defHttp.put({ url: Api.putReadMessage });
}
