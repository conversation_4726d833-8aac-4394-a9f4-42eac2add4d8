import { defHttp } from '@/utils/http/axios';

export const urlHeader = {
  'Content-Type': 'application/x-www-form-urlencoded',
};
export const formHeader = {
  'Content-Type': 'multipart/form-data',
};

//统一导出excel
export const exportExcel = (url, params) =>
  defHttp.post(
    {
      url,
      params,
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
    {
      isTransformResponse: false,
    },
  );
