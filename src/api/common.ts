import { defHttp } from '@/utils/http/axios';

enum Api {
  //获取OSS文件列表
  getOssListByIds = '/system/oss/listByIds',
  //获取部门列表 - 无权限
  getDeptList = '/com/dept/list',
  //获取部门列表 - 无权限
  getUserList = '/com/user/list',
  // 获取指定用户信息 - 单多用户 - 无权限
  getUserInfos = '/com/user/info',
  getRoleList = '/com/role/list',
  //获取设备列表
  getDeviceList = '/com/device/list',
  //获取菜单列表
  getMenuList = '/com/menu/list',

  //获取我上传的文件
  getMyUploadFile = '/system/oss/my/list',
}

export const getOssListByIds = (params) =>
  defHttp.post({
    url: Api.getOssListByIds,
    params,
  });

export const getDeptList = (params) =>
  defHttp.post(
    {
      url: Api.getDeptList,
      params,
    },
    { joinParamsToUrl: true },
  );

export const getUserList = (params) =>
  defHttp.post({
    url: Api.getUserList,
    params,
  });

export const getUserInfos = (params) =>
  defHttp.post({
    url: Api.getUserInfos,
    params,
  });
//获取角色列表
export const getRoleList = (params?) =>
  defHttp.post({
    url: Api.getRoleList,
    params,
  });

export const getMenuList = (params?) =>
  defHttp.post({
    url: Api.getMenuList,
    params,
  });

export const getDeviceList = (params?) =>
  defHttp.post({
    url: Api.getDeviceList,
    params,
  });
//获取我上传的文件
export const getMyUploadFile = (params) =>
  defHttp.post({
    url: Api.getMyUploadFile,
    params,
  });
