import { defHttp } from '@/utils/http/axios';
import { exportExcel } from '../baseApi';

enum Api {
  //排班组
  getScheduledTeamList = '/system/scheduledTeam/list',
  addScheduledTeam = '/system/scheduledTeam',
  getScheduledTeamById = '/system/scheduledTeam/{id}',
  editScheduledTeam = '/system/scheduledTeam/edit',
  deleteScheduledTeam = '/system/scheduledTeam/delete/{id}',

  //排班组内班组
  getTeamGroupList = '/system/scheduledTeamGroup/list',
  addTeamGroup = '/system/scheduledTeamGroup',
  getTeamGroupById = '/system/scheduledTeamGroup/{id}',
  editTeamGroup = '/system/scheduledTeamGroup/edit',
  deleteTeamGroup = '/system/scheduledTeamGroup/delete/{id}',
  getOtherTeamGroupMemberUser = '/system/scheduledTeamGroup/join/{teamId}',

  //排班计划
  scheduledPlanList = '/scheduled/scheduledPlan/list', //列表
  addScheduledPlan = '/scheduled/scheduledPlan', //新增
  scheduledPlanById = '/scheduled/scheduledPlan/{id}', //详细信息
  editScheduledPlan = '/scheduled/scheduledPlan/edit', //修改
  scheduledPlanExport = '/scheduled/scheduledPlan/export', //导出
  scheduledPlanEnablePlan = '/scheduled/scheduledPlan/enablePlan', //开启
  scheduledPlanClosePlan = '/scheduled/scheduledPlan/closePlan/{planId}', //关闭
}

//查询排班组列表
export const getScheduledTeamList = (params) => defHttp.post({ url: Api.getScheduledTeamList, params });

//新增排班组
export const addScheduledTeam = (params) => defHttp.post({ url: Api.addScheduledTeam, params });

//获取排班组详细信息
export const getScheduledTeamById = (params) => defHttp.post({ url: Api.getScheduledTeamById, params });

//修改排班组
export const editScheduledTeam = (params) => defHttp.post({ url: Api.editScheduledTeam, params });

//删除排班组
export const deleteScheduledTeam = (params) => defHttp.post({ url: Api.deleteScheduledTeam, params });

//查询排班组列表
export const getTeamGroupList = (params) => defHttp.post({ url: Api.getTeamGroupList, params });

//新增班组
export const addTeamGroup = (params) => defHttp.post({ url: Api.addTeamGroup, params });

//获取班组详细信息
export const getTeamGroupById = (params) => defHttp.post({ url: Api.getTeamGroupById, params });

//修改班组
export const editTeamGroup = (params) => defHttp.post({ url: Api.editTeamGroup, params });

//删除排班组内班组
export const deleteTeamGroup = (params) => defHttp.post({ url: Api.deleteTeamGroup, params });

// 获取已分配进该工作组的其他班组的用户信息
export const getOtherTeamGroupMemberUser = (params) =>
  defHttp.post({
    url: params?.groupId ? `${Api.getOtherTeamGroupMemberUser}/{groupId}` : Api.getOtherTeamGroupMemberUser,
    params,
  });

//查询排班计划列表
export const scheduledPlanList = (params) => defHttp.post({ url: Api.scheduledPlanList, params });

//新增排班计划
export const addScheduledPlan = (params) => defHttp.post({ url: Api.addScheduledPlan, params });

//获取排班计划详细信息
export const scheduledPlanById = (params) => defHttp.post({ url: Api.scheduledPlanById, params });

//修改排班计划
export const editScheduledPlan = (params) => defHttp.post({ url: Api.editScheduledPlan, params });

//导出排班计划列表
export const scheduledPlanExport = (params) => exportExcel(Api.scheduledPlanExport, params);

//开启排班计划
export const scheduledPlanEnablePlan = (params) => defHttp.post({ url: Api.scheduledPlanEnablePlan, params });

//关闭排班计划
export const scheduledPlanClosePlan = (params) => defHttp.get({ url: Api.scheduledPlanClosePlan, params });
