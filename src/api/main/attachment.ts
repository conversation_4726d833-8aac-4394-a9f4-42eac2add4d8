import { defHttp } from '@/utils/http/axios';
enum Api {
  //附件管理
  fileGroupList = '/system/fileGroup/list', //查询文件分组列表
  deleteFileGroup = '/system/fileGroup/delete/', //删除分组
  addFileGroup = '/system/fileGroup', //新增分组
  updateFileGroup = '/system/fileGroup/edit', //修改分组
  fileDataList = '/system/fileData/list', //根据分组查询附件列表
  deleteFileData = '/system/fileData/delete/', //删除附件
  addFileData = '/system/fileData', //新增附件
  updateFileData = '/system/fileData/edit', //修改附件
}

//附件管理-查询文件分组列表
export const fileGroupList = (params?) => defHttp.post({ url: Api.fileGroupList, params });
//附件管理-删除分组
export const deleteFileGroup = (id) => defHttp.post({ url: Api.deleteFileGroup + id });
//附件管理-新增分组
export const addFileGroup = (params) => defHttp.post({ url: Api.addFileGroup, params });
//附件管理-修改分组
export const updateFileGroup = (params) => defHttp.post({ url: Api.updateFileGroup, params });
//附件管理-根据分组查询附件列表
export const fileDataList = (params) => defHttp.post({ url: Api.fileDataList, params });
//附件管理-删除附件
export const deleteFileData = (id) => defHttp.post({ url: Api.deleteFileData + id });
//附件管理-新增附件
export const addFileData = (params) => defHttp.post({ url: Api.addFileData, params });
//附件管理-修改附件
export const updateFileData = (params) => defHttp.post({ url: Api.updateFileData, params });
