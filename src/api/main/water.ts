import { defHttp } from '@/utils/http/axios';
import { exportExcel } from '@/api/baseApi';

enum Api {
  getWaterItems = '/water/getWaterItems',
  editWaterItems = '/water/editWaterItems',
  DynamicBean = '/water/DynamicBean',
  listMana = '/water/listMana',
  IDUsernameMp = '/water/IDUsernameMp',
  getIDStationNames = '/water/getIDStationNames',
  addMana = '/water/addMana', //新增|修改
  getManaById = '/water/getManaById', //新增|修改岗位
  editMana = '/water/editMana',
  delMana = '/water/delMana',
  echartsData = '/water/echartsData',
  report = '/water/report',
  getReportSchema = '/water/getReportSchema',
  exportReport = '/water/exportReport',
  getDateRange = '/water/getDateRange',
  getDateRange2 = '/water/getDateRange2',
  queryLineCharts = '/water/queryLineCharts',
  getUsedWaterItems = '/water/getUsedWaterItems',
}

// 获取设备分类清单列表
export const getWaterItems = () => defHttp.get({ url: Api.getWaterItems });
// 修改监测项
export const editWaterItems = (params) => defHttp.get({ url: Api.editWaterItems, params });
// 获取动态表头
export const DynamicBean = () => defHttp.get({ url: Api.DynamicBean });
// 监测列表
export const listMana = (params) => defHttp.get({ url: Api.listMana, params });
// 人员下拉
export const IDUsernameMp = () => defHttp.get({ url: Api.IDUsernameMp });
// 设备下拉
export const getIDStationNames = () => defHttp.get({ url: Api.getIDStationNames });
// 新增监测
export const addMana = (params) => defHttp.get({ url: Api.addMana, params });
// 监测详情
export const getManaById = (params) => defHttp.get({ url: Api.getManaById, params });

export const editMana = (params) => defHttp.get({ url: Api.editMana, params });

export const delMana = (params) => defHttp.get({ url: Api.delMana, params });
// 分析表单
export const echartsData = () => defHttp.get({ url: Api.echartsData });

export const report = (params) => defHttp.get({ url: Api.report, params });
export const getReportSchema = () => defHttp.get({ url: Api.getReportSchema });
export const exportReport = (params) => exportExcel(Api.exportReport, params);
// 根据年份,周数.获取日期区间
export const getDateRange = (params) => defHttp.get({ url: Api.getDateRange, params });
export const getDateRange2 = (params) => defHttp.get({ url: Api.getDateRange2, params });
// 查询折线图数据
export const queryLineCharts = () => defHttp.get({ url: Api.queryLineCharts });
// 获取在用的监测项
export const getUsedWaterItems = () => defHttp.get({ url: Api.getUsedWaterItems });
