import { defHttp } from '@/utils/http/axios';
import { formHeader, urlHeader } from '../baseApi';
import type {
  CollectionTypeResponse,
  HouseStatusStatisticsResponse,
  NumericalDataStatisticsResponse,
  NotPaymentMonthTimeResponse,
} from '../../types/workbench';

enum Api {
  //个人中心
  getUserProfile = '/system/user/profile', //个人信息
  userProfileUpdatePwd = '/system/user/profile/updatePwd', //重置密码
  userProfileAvatar = '/system/user/profile/avatar', //头像上传
  //代码生成
  getGenList = '/tool/gen/list',
  getGenDbList = '/tool/gen/db/list',
  importGenTable = '/tool/gen/importTable',
  getGenById = '/tool/gen/',
  updateGenTable = '/tool/gen',
  deleteGenById = '/tool/gen/',
  synchDb = '/tool/gen/synchDb/',
  batchGenCode = '/tool/gen/batchGenCode',
  genPreview = '/tool/gen/preview/',

  //工作台
  getToDoList = '/bpm/workspace/process/toDoList',
  getDoneList = '/bpm/workspace/process/doneList',
  getApplyList = '/bpm/workspace/process/applyList',
  getccList = '/bpm/workspace/process/ccList',

  // 更新用户快捷配置
  updateUserShortcutConfig = '/system/userShortcutConfig/edit/{attr}/type',
  getUserShortcutConfig = '/system/userShortcutConfig/my',
  getUserShortcutConfigVo = '/system/userShortcutConfig/my/vo',
  getDefaultUserShortcutConfig = '/system/userShortcutConfig/default',

  // 工作台统计接口
  numericalDataStatistics = '/gzf/workTable/numericalDataStatistics', // 数值数据统计
  statisticsHousingYears = '/gzf/workTable/statisticsHousingYears', // 统计房屋年限
  vacancyRateRanking = '/gzf/workTable/kongzhi/yuanluo', // 空置率排名
  contractSituation = '/gzf/workTable/sameTotal', // 合同情况
  expiryStatistics = '/gzf/leaseInfo/linqi/statistics', // 统计三个月内的临期，到期数据
  houseStatusStatistics = '/gzf/workTable/houseStatusStatistics', // 房屋状态
  collectionTypeStatistics = '/gzf/workTable/collectionType', // 收款类别占比
  communityIncomeStatistics = '/gzf/workTable/communityIncome', // 小区收入统计
  paymentMethodStatistics = '/gzf/workTable/paymentMethodStatistics', // 支付方式

  // 工作台权限类接口（小区权限类）
  permissionCollectionType = '/gzf/workTablePermission/collectionType', // 收款类别占比统计
  permissionHouseStatusStatistics = '/gzf/workTablePermission/houseStatusStatistics', // 房屋状态统计
  permissionNumericalDataStatistics = '/gzf/workTablePermission/numericalDataStatistics', // 工作台-数值数据统计
  notPaymentMonthTime = '/gzf/workTable/notPaymentMonthTime', // 未缴费统计-3 6 9 12月
}
const headers = {
  Datasource: 'master',
};
//获取代码模板列表
export const getGenList = (params) =>
  defHttp.post({
    url: Api.getGenList,
    params,
    headers,
  });
//获取数据库列表
export const getGenDbList = (params) =>
  defHttp.post({
    url: Api.getGenDbList,
    params,
    headers,
  });
//导入表
export const importGenTable = (params) =>
  defHttp.post({
    url: Api.importGenTable,
    params,
    headers: {
      ...headers,
      ...urlHeader,
    },
  });
//获取代码生成信息
export const getGenById = (id) =>
  defHttp.get({
    url: Api.getGenById + id,
    headers,
  });
//更新代表生成表
export const updateGenTable = (params) =>
  defHttp.put({
    url: Api.updateGenTable,
    params,
    headers,
  });
//根据id删除生成表
export const deleteGenById = (id) =>
  defHttp.delete({
    url: Api.deleteGenById + id,
    headers,
  });
//同步数据库
export const synchDb = (name) =>
  defHttp.get({
    url: Api.synchDb + name,
    headers,
  });
//批量生成代码
export const batchGenCode = (params) =>
  defHttp.get(
    {
      url: Api.batchGenCode,
      params,
      headers,
      responseType: 'blob',
    },
    { isTransformResponse: false },
  );
//预览生成的代码
export const previewCode = (id) =>
  defHttp.get({
    url: Api.genPreview + id,
    headers,
  });

//个人中心个人信息
export const getUserProfile = (params?) => defHttp.get({ url: Api.getUserProfile, params });
//个人中心修改用户
export const UpUserProfile = (params) => defHttp.put({ url: Api.getUserProfile, params });
//个人中心重置密码
export const userProfileUpdatePwd = (params) =>
  defHttp.put({ url: Api.userProfileUpdatePwd, params, headers: urlHeader });
//个人中心头像上传
export const userProfileAvatar = (data) =>
  defHttp.post({
    url: Api.userProfileAvatar,
    headers: formHeader,
    data,
  });

//查看我的待办
export const getToDoList = (params) => defHttp.post({ url: Api.getToDoList, params });
//查看我的已办
export const getDoneList = (params) => defHttp.post({ url: Api.getDoneList, params });
//查看我发起的流程
export const getApplyList = (params) => defHttp.post({ url: Api.getApplyList, params });
//抄送我的
export const getccList = (params) => defHttp.post({ url: Api.getccList, params });
// 更新用户快捷配置
export const updateUserShortcutConfig = (params) => defHttp.post({ url: Api.updateUserShortcutConfig, params });
//获取用户快捷配置
export const getUserShortcutConfig = (params?) => defHttp.post({ url: Api.getUserShortcutConfig, params });
//获取我的配置详细信息-vo
export const getUserShortcutConfigVo = (params?) => defHttp.post({ url: Api.getUserShortcutConfigVo, params });
//获取默认配置详细信息
export const getDefaultUserShortcutConfig = (params?) =>
  defHttp.post({ url: Api.getDefaultUserShortcutConfig, params });

// 工作台统计接口
// 数值数据统计
export const getNumericalDataStatistics = (params?) => defHttp.post({ url: Api.numericalDataStatistics, params });

// 统计房屋年限
export const getStatisticsHousingYears = (params?) => defHttp.post({ url: Api.statisticsHousingYears, params });

// 空置率排名
export const getVacancyRateRanking = (params?) => defHttp.post({ url: Api.vacancyRateRanking, params });

// 合同情况
export const getContractSituation = (params?) => defHttp.post({ url: Api.contractSituation, params });

// 统计三个月内的临期，到期数据
export const getExpiryStatistics = (params?) => defHttp.post({ url: Api.expiryStatistics, params });

// 房屋状态
export const getHouseStatusStatistics = (params?) => defHttp.post({ url: Api.houseStatusStatistics, params });

// 收款类别占比
export const getCollectionTypeStatistics = (params?) => defHttp.post({ url: Api.collectionTypeStatistics, params });

// 小区收入统计
export const getCommunityIncomeStatistics = (params?) => defHttp.post({ url: Api.communityIncomeStatistics, params });

// 支付方式
export const getPaymentMethodStatistics = (params?) => defHttp.post({ url: Api.paymentMethodStatistics, params });

// 工作台权限类接口（小区权限类）
// 收款类别占比统计
export const getPermissionCollectionType = (params?: any): Promise<CollectionTypeResponse> =>
  defHttp.post({ url: Api.permissionCollectionType, params });

// 房屋状态统计
export const getPermissionHouseStatusStatistics = (params?: any): Promise<HouseStatusStatisticsResponse> =>
  defHttp.post({ url: Api.permissionHouseStatusStatistics, params });

// 工作台-数值数据统计
export const getPermissionNumericalDataStatistics = (params?: any): Promise<NumericalDataStatisticsResponse> =>
  defHttp.post({ url: Api.permissionNumericalDataStatistics, params });

// 未缴费统计-3 6 9 12月
export const getNotPaymentMonthTime = (params?: any): Promise<NotPaymentMonthTimeResponse> =>
  defHttp.post({ url: Api.notPaymentMonthTime, params });
