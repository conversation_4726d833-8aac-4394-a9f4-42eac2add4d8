import { defHttp } from '@/utils/http/axios';
enum Api {
  //流程模型组
  addProcessGroup = '/process/group',
  getProcessGroupList = '/process/group/list',
  //模型管理
  addProcessTemplate = '/process/template',
  getProcessTemplateList = '/process/template/list',
  getProcessTemplateHistory = '/process/template/list/history',
  getProcessTemplateById = '/process/template/form/detail/{id}',
  getProcessTemplateTriggerInfo = '/process/template/triggerInfo/list',
  //流程表单
  addProcessForm = '/bpm/setForm',
  getProcessFormList = '/bpm/setForm/list',
  // deleteProcessForm = '/bpm/setForm/{id}',
  editProcessForm = '/bpm/setForm/edit',
  getProcessFormById = '/bpm/setForm/{id}',
  startProcess = '/bpm/workspace/process/start',
  getFlowModalInfo = '/bpm/workspace/process/instanceInfo',
  argeeFlow = '/bpm/workspace/agree',
  refuseFlow = '/bpm/workspace/refuse',
  revokeFlow = '/bpm/workspace/revoke',
  getProcessRecordListById = '/bpm/workspace/process/record/{id}',
  getProcessRollbackNodes = '/bpm/workspace/rollbackNodes/{id}',
  rollbackFlow = '/bpm/workspace/rollback',
  getBusinessKeyList = '/process/template/form/business',

  assigneeFlow = '/bpm/workspace/assignee',
  //流程设置
  getProcessTemplateByKey = '/process/template/form/use/{key}',
  getProcessImgById = '/bpm/api/show/png/{id}',
  //获取流程表单详情
  getProcessFormDetail = '/bpm/manage/process/instanceInfo/{processInstanceId}',

  //流程草稿
  addWorkDraft = '/bpm/workDraft',
  getWorkDraftList = '/bpm/workDraft/my/list',
  getWorkDrafById = '/bpm/workDraft/{id}',
  deleteWorkDrafById = '/bpm/workDraft/{ids}',
  editWorkDraft = '/bpm/workDraft/edit',
  //流程管理
  getProcessManageList = '/bpm/manage/processInstance',
  //管理员撤销流程
  revokeManageFlow = '/bpm/manage/revoke',
  //管理员转办流程
  assigneeManageFlow = '/bpm/manage/assignee',
  //管理员修改表单
  editManageForm = '/bpm/manage/updateForm',
}

//新增流程模型组
export const addProcessGroup = (params) => defHttp.post({ url: Api.addProcessGroup, params });
//获取流程模型组列表
export const getProcessGroupList = (params) => defHttp.post({ url: Api.getProcessGroupList, params });
//新增流程模型
export const addProcessTemplate = (params) => defHttp.post({ url: Api.addProcessTemplate, params });
//获取流程模型列表
export const getProcessTemplateList = (params) => defHttp.post({ url: Api.getProcessTemplateList, params });
//获取流程模型历史版本
export const getProcessTemplateHistory = (params) => defHttp.post({ url: Api.getProcessTemplateHistory, params });
//获取流程模型详情
export const getProcessTemplateById = (params) => defHttp.get({ url: Api.getProcessTemplateById, params });
//新增流程表单
export const addProcessForm = (params) => defHttp.post({ url: Api.addProcessForm, params });
//获取流程表单列表
export const getProcessFormList = (params) => defHttp.post({ url: Api.getProcessFormList, params });
//编辑流程表单
export const editProcessForm = (params) => defHttp.post({ url: Api.editProcessForm, params });
//获取流程表单详情
export const getProcessFormById = (params) => defHttp.get({ url: Api.getProcessFormById, params });
//获取流程模型
export const getProcessTemplateByKey = (params) => defHttp.get({ url: Api.getProcessTemplateByKey, params });
//获取业务key列表
export const getBusinessKeyList = () => defHttp.get({ url: Api.getBusinessKeyList });
//启动工作流
export const startPorcess = (params) => defHttp.post({ url: Api.startProcess, params });
//获取工作流详情
export const getFlowModalInfo = (params) => defHttp.post({ url: Api.getFlowModalInfo, params });

//同意工作流
export const argeeFlow = (params) => defHttp.post({ url: Api.argeeFlow, params });

//获取触发器信息
export const getProcessTemplateTriggerInfo = (params?) =>
  defHttp.get({ url: Api.getProcessTemplateTriggerInfo, params });
//获取操作记录
export const getProcessRecordListById = (params) => defHttp.post({ url: Api.getProcessRecordListById, params });
//获取回退节点
export const getProcessRollbackNodes = (params) => defHttp.post({ url: Api.getProcessRollbackNodes, params });
//回退流程
export const rollbackFlow = (params) => defHttp.post({ url: Api.rollbackFlow, params });
//拒绝流程
export const refuseFlow = (params) => defHttp.post({ url: Api.refuseFlow, params });
//撤销流程
export const revokeFlow = (params) => defHttp.post({ url: Api.revokeFlow, params });
//转办流程
export const assigneeFlow = (params) => defHttp.post({ url: Api.assigneeFlow, params });

//获取流程表单详情，
export const getProcessFormDetail = (params) => defHttp.post({ url: Api.getProcessFormDetail, params });

//新增流程草稿
export const addWorkDraft = (params) => defHttp.post({ url: Api.addWorkDraft, params });
//查询我的草稿列表
export const getWorkDraftList = (params) => defHttp.post({ url: Api.getWorkDraftList, params });
//获取流程工作草稿详细信息
export const getWorkDraftById = (params) => defHttp.get({ url: Api.getWorkDrafById, params });
//删除流程工作草稿
export const deleteWorkDraftById = (params) => defHttp.post({ url: Api.deleteWorkDrafById, params });
//修改工作流草稿
export const editWorkDraft = (params) => defHttp.post({ url: Api.editWorkDraft, params });

//查询流程管理列表
export const getProcessManageList = (params) => defHttp.post({ url: Api.getProcessManageList, params });
//管理员撤销流程
export const revokeManageFlow = (params) => defHttp.post({ url: Api.revokeManageFlow, params });
//管理员转办流程
export const assigneeManageFlow = (params) => defHttp.post({ url: Api.assigneeManageFlow, params });
//获取流程图片
export const getProcessImgById = (params) =>
  defHttp.get(
    {
      url: Api.getProcessImgById,
      params,
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
    {
      isTransformResponse: false,
    },
  );
//管理员修改表单
export const editManageForm = (params) => defHttp.post({ url: Api.editManageForm, params });
