import { defHttp } from '@/utils/http/axios';
enum Api {
  getSystemTagByType = '/system/tag/type/{type}', //根据类型获取标签
  getSystemTagList = '/system/tag/list', //获取标签列表
  addSystemTag = '/system/tag', //新增标签
  addRkDoc = '/rk/doc', //新增文档
  getRkDocList = '/rk/doc/list', //获取文档列表
  editRkDoc = '/rk/doc/edit', //编辑文档
  getRkDocById = '/rk/doc/{id}', //根据id获取文档
}

export const getSystemTagByType = (params) => defHttp.post({ url: Api.getSystemTagByType, params });
export const addSystemTag = (params) => defHttp.post({ url: Api.addSystemTag, params });
export const addRkDoc = (params) => defHttp.post({ url: Api.addRkDoc, params });
export const editRkDoc = (params) => defHttp.post({ url: Api.editRkDoc, params });
export const getSystemTagList = (params) => defHttp.post({ url: Api.getSystemTagList, params });
export const getRkDocById = (params) => defHttp.post({ url: Api.getRkDocById, params });
export const getRkDocList = (params) => defHttp.post({ url: Api.getRkDocList, params });
