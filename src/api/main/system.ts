import { defHttp } from '@/utils/http/axios';
import { exportExcel, formHeader, urlHeader } from '../baseApi';

enum Api {
  //菜单管理
  getMenuList = '/system/menu/list',
  getMenuById = '/system/menu/',
  deleteMenuById = '/system/menu/',
  upOrAddMenu = '/system/menu',
  getMenuTree = '/system/menu/treeselect',
  //角色管理
  getRoleList = '/system/role/list',
  changeRoleStatus = '/system/role/changeStatus',
  getRoleById = '/system/role/',
  getRoleMenuById = '/system/menu/roleMenuTreeselect/',
  upOrAddRole = '/system/role',
  deleteRoleById = '/system/role/',
  exportRoleList = '/system/role/export',
  getRoleAuthUserList = '/system/role/authUser/allocatedList',
  getUnRoleAuthUserList = '/system/role/authUser/unallocatedList',
  cancelAuthRole = '/system/role/authUser/cancelAll',
  selectAuthRole = '/system/role/authUser/selectAll',
  //部门管理
  getDeptList = '/system/dept/list',
  getDeptById = '/system/dept/',
  getDeptMenuById = '/system/dept/list/exclude/',
  upOrAddDept = '/system/dept',
  deleteDeptById = '/system/dept/',
  //角色管理
  getUserDeptTree = '/system/user/deptTree',
  getUserList = '/system/user/list',
  setUserStatus = '/system/user/changeStatus',
  getAuthRoleById = '/system/user/authRole/',
  setAuthRole = '/system/user/authRole',
  getUserOption = '/system/user/',
  isExistUser = '/system/user/duplicateName/',
  upOrAddUser = '/system/user',
  getUserById = '/system/user/',
  getUserInfoById = '/system/user/courtyard/',
  deleteUserById = '/system/user/',
  resetPwd = '/system/user/resetPwd/',
  exportUserDemo = '/system/user/importTemplate',
  exportUserList = '/system/user/export',
  importUserLIst = '/system/user/importData',
  getDeptTreeById = '/system/role/deptTree/',
  setDataScope = '/system/role/dataScope',
  systemUserUnlock = '/system/user/unlock/',

  //操作日志
  getOperLogList = '/monitor/operlog/list',
  getOperlogById = '/monitor/operlog/',
  monitorOperlogClean = '/monitor/operlog/clean',
  monitorOperlogExport = '/monitor/operlog/export',
  //登录日志
  getLoginLogList = '/monitor/logininfor/list',
  cleanLoginLog = '/monitor/logininfor/clean',
  deleteLoginLogById = '/monitor/logininfor/',
  exportLoginLog = '/monitor/logininfor/export',
  //字典管理
  getDictTypeList = '/system/dict/type/list', //查询字典类型列表
  getDictTypeById = '/system/dict/type/', //查询字典类型详细
  upOrAddDictType = '/system/dict/type', //更新或新增字典类型
  getDictDataTypeById = '/system/dict/data/type/', //根据字典类型查询字典数据信息
  getDictTypeRefreshCache = '/system/dict/type/refreshCache', //刷新字典缓存
  getDictTypeOptionselect = '/system/dict/type/optionselect', //获取字典选择框列表
  getDictDataList = '/system/dict/data/list', //查询字典数据列表
  getDictDataById = '/system/dict/data/', //查询字典数据详细
  upOrAddDictData = '/system/dict/data', //更新或新增字典类型数据
  dictTypeExport = '/system/dict/type/export', //导出字典类型列表
  dictDataExport = '/system/dict/data/export', //导出字典数据列表
  //文件管理
  getFileInfoList = '/system/oss/list', //查询文件列表
  ossUpload = '/system/oss/upload',
  ossDownload = '/system/oss/download/', //下载文件
  deleteOssById = '/system/oss/',
  getOssConfigList = '/system/oss/config/list',
  setOssConfigStatus = '/system/oss/config/changeStatus',
  deleteOssCOnfigByid = '/system/oss/config/',
  getOssConfigById = '/system/oss/config/',
  upOrAddOssConfig = '/system/oss/config',
  //参数设置
  getConfigList = '/system/config/list', //获取参数配置列表
  getConfigById = '/system/config/', //根据参数编号获取详细信息
  upOrAddConfig = '/system/config', //更新或新增参数设置
  getConfigRefreshCache = '/system/config/refreshCache', //刷新参数设置
  getConfigConfigKey = '/system/config/configKey/', //根据参数键名查询参数值
  configUpdateByKey = '/system/config/updateByKey', //根据参数键名修改参数配置
  configExport = '/system/config/export', //导出参数设置列表
  //岗位管理
  getPostList = '/system/post/list', //获取岗位列表
  getPostById = '/system/post/', //根据岗位编号获取详细信息
  upOrAddPost = '/system/post', //新增|修改岗位
  deletePostById = '/system/post/', //删除岗位
  exportPostList = '/system/post/export', //导出字典数据列表
  //通知公告
  getNoticeList = '/system/notice/list', //获取通知公告列表
  getNoticeById = '/system/notice/', //根据公告id获取详细信息
  upOrAddNotice = '/system/notice', //新增|修改公告
  deleteNoticeById = '/system/notice/', //删除公告
  //基础数据管理
  controlDataList = '/system/controlData/list', //查询基础自控数据管理列表
  controlDataListAll = '/system/controlData/listAll',

  getControlDataById = '/system/controlData/', //获取基础自控数据管理详细信息
  addControlData = '/system/controlData', //新增基础自控数据管理
  editControlData = '/system/controlData/edit', //修改基础自控数据管理
  //基础数据管理-属性管理
  controlDataFieldsList = '/system/controlDataFields/list', //查询基础数据列表属性列表
  controlDataFieldsListAll = '/system/controlDataFields/listAll',
  getControlDataFieldsById = '/system/controlDataFields/', //获取基础数据属性管理详细信息
  addControlDataFields = '/system/controlDataFields', //新增基础数据属性管理
  editControlDataFields = '/system/controlDataFields/edit', //修改基础数据管理属性管理

  //消息模板
  getMsgTemplateList = '/system/messageTemplate/list', //查询消息模板列表
  getMsgTemplateById = '/system/messageTemplate/{id}', //获取消息模板详细信息
  addMsgTemplate = '/system/messageTemplate', //新增消息模板
  editMsgTemplate = '/system/messageTemplate/edit', //修改消息模板
  deleteMsgTemplate = '/system/messageTemplate/{ids}', //删除消息模板
  exportMsgTemplate = '/system/messageTemplate/export', //导出消息模板列表
  messageTemplateQueryTest = '/system/messageTemplate/queryTest/', //消息模板测试 详情
  messageTemplateTest = '/system/messageTemplate/test', //模板发送测试

  //App管理
  getVersionList = '/system/version/list',
  //删除app管理
  deleteVersionById = '/system/version/{id}',
  //新增app
  addVersion = '/system/version',
  //修改app
  editVersion = '/system/version/edit',
  //获取app管理详细信息
  getVersionById = '/system/version/{id}',

  //查询工作台配置列表
  getWorkstationSetList = '/system/workstationSet/list',
  //新增工作台配置
  addWorkstationSet = '/system/workstationSet',
  //删除工作台配置
  deleteWorkstationSet = '/system/workstationSet/{configIds}',
  //修改工作台配置
  editWorkstationSet = '/system/workstationSet/edit',
  //导出工作台配置列表
  exportWorkstationSet = '/system/workstationSet/export',
  //获取工作台配置详细信息
  getWorkstationSetById = '/system/workstationSet/{configId}',

  //周期管理-时间点定义
  baseTimesList = '/system/baseTimes/list', //查询系统基础设置天内次数设置列表
  baseTimesById = '/system/baseTimes/', //获取系统基础设置天内次数设置详细信息
  addBaseTimes = '/system/baseTimes', //新增系统基础设置天内次数设置
  editBaseTimes = '/system/baseTimes/edit', //修改系统基础设置天内次数设置

  //系统基础设置频次设置
  getBaseFrequencyList = '/system/baseFrequency/list',
  getBaseFrequencyById = '/system/baseFrequency/{id}',
  deleteBaseFrequencyById = '/system/baseFrequency/{id}',
  updateBaseFrequency = '/system/baseFrequency/edit',
  addBaseFrequency = '/system/baseFrequency',

  // 事件工单
  getEventOrdertList = '/system/orderEventReport/list',
  getEventOrderById = '/system/orderEventReport/{id}',
  deleteEventOrderById = '/system/orderEventReport/del/{id}',
  exportEventOrder = '/system/orderEventReport/export',
  //系统基础设置频次执行实例
  getBaseFrequencyInstanceList = '/system/baseFrequency/instance/list',
  getBaseFrequencyInstanceById = '/system/baseFrequency/instance/{id}',
  deleteBaseFrequencyInstanceById = '/system/baseFrequency/instance/{id}',
}
//获取菜单列表
export const getMenuList = (params) =>
  defHttp.post({
    url: Api.getMenuList,
    params,
  });
//根据菜单id获取菜单
export const getMenuById = (menuId) => defHttp.get({ url: Api.getMenuById + menuId });
//获取菜单树
export const getMenuTree = (params?) => defHttp.get({ url: Api.getMenuTree, params });
//更新或新增菜单
export const updateMenu = (method, params) => defHttp[method]({ url: Api.upOrAddMenu, params });
//删除菜单
export const deleteMenu = (menuId) => defHttp.delete({ url: Api.deleteMenuById + menuId });

//获取角色列表
export const getRoleListByPage = (params) => defHttp.post({ url: Api.getRoleList, params });
//根据角色id获取角色
export const getRoleById = (roleId) => defHttp.get({ url: Api.getRoleById + roleId });
//获取角色菜单
export const getRoleMenuById = (roleId) => defHttp.get({ url: Api.getRoleMenuById + roleId });
//更新或新增角色
export const updateRole = (method, params) => defHttp[method]({ url: Api.upOrAddRole, params });
//设置角色状态
export const setRoleStatus = (roleId, status) => defHttp.put({ url: Api.changeRoleStatus, params: { roleId, status } });
//删除角色
export const deleteRole = (roleId) => defHttp.delete({ url: Api.deleteRoleById + roleId });
//导出角色列表
export const exportRoleList = (params) => exportExcel(Api.exportRoleList, params);
//获取角色已授权用户列表
export const getRoleAuthUserList = (params) => defHttp.post({ url: Api.getRoleAuthUserList, params });
//获取角色未授权用户列表
export const getUnRoleAuthUserList = (params) => defHttp.post({ url: Api.getUnRoleAuthUserList, params });
//取消授权用户
export const cancelAuthRole = (params) =>
  defHttp.put({
    url: Api.cancelAuthRole,
    params,
    headers: urlHeader,
  });
//授权用户
export const selectAuthRole = (params) =>
  defHttp.put({
    url: Api.selectAuthRole,
    params,
    headers: urlHeader,
  });

//获取部门列表
export const getDeptList = (params) => defHttp.post({ url: Api.getDeptList, params });
//根据部门id获取部门菜单
export const getDeptMenuById = (deptId) => defHttp.get({ url: Api.getDeptMenuById + deptId });
//根据部门id获取部门
export const getDeptById = (deptId) => defHttp.get({ url: Api.getDeptById + deptId });
//更新或新增部门
export const updateDept = (method, params) => defHttp[method]({ url: Api.upOrAddDept, params });
//删除部门
export const deleteDept = (deptId) => defHttp.delete({ url: Api.deleteDeptById + deptId });

//获取用户部门树
export const getUserDeptTree = () => defHttp.get({ url: Api.getUserDeptTree });
//获取用户列表
export const getUserList = (params) => defHttp.post({ url: Api.getUserList, params });
//设置用户状态
export const setUserStatus = (userId, status) => defHttp.put({ url: Api.setUserStatus, params: { userId, status } });

//根据id获取用户权限
export const getAuthRole = (userId) => defHttp.get({ url: Api.getAuthRoleById + userId });
//授权角色
export const updateAuthRole = (userId, roleIds) =>
  defHttp.post({
    url: Api.setAuthRole,
    data: {
      userId,
      roleIds,
    },
    headers: urlHeader,
  });
//获取新增/修改用户的岗位和角色选项
export const getUserOption = () => defHttp.get({ url: Api.getUserOption });

//判断用户是否存在
export const isExistUser = (userName) => defHttp.get({ url: Api.isExistUser + userName }, { errorMessageMode: 'none' });
//更新或新增用户
export const updateUser = (method, params) => defHttp[method]({ url: Api.upOrAddUser, params });
//根据id获取用户
export const getUserById = (userId) => defHttp.get({ url: Api.getUserById + userId });
//根据id获取用户详细信息
export const getUserInfoById = (userId) => defHttp.get({ url: Api.getUserInfoById + userId });

//用户解除锁定
export const systemUserUnlock = (userName) => defHttp.get({ url: Api.systemUserUnlock + userName });
//删除用户
export const deleteUser = (userId) => defHttp.delete({ url: Api.deleteUserById + userId });
//重置密码
export const resetPwd = (userId) => defHttp.put({ url: Api.resetPwd + userId });
//导出用户上传模板
export const exportUserDemo = () => exportExcel(Api.exportUserDemo, {});
//导出用户列表
export const exportUserList = (params) => exportExcel(Api.exportUserList, params);
//导入用户列表
export const importUserList = (params, data) =>
  defHttp.post(
    {
      url: Api.importUserLIst,
      params,
      data,
      headers: urlHeader,
    },
    {
      isTransformResponse: false,
    },
  );
//根据部门id获取部门树
export const getDeptTreeById = (deptId) => defHttp.get({ url: Api.getDeptTreeById + deptId });
//设置部门权限
export const setDataScope = (params) =>
  defHttp.put({
    url: Api.setDataScope,
    params,
  });
//获取操作日志
export const getOperLogList = (params) => defHttp.post({ url: Api.getOperLogList, params });
//批量删除操作日志记录
export const getOperlogById = (Id) => defHttp.delete({ url: Api.getOperlogById + Id });
//清理操作日志记录
export const monitorOperlogClean = () => defHttp.delete({ url: Api.monitorOperlogClean });
//导出操作日志记录列表
export const monitorOperlogExport = (params) => exportExcel(Api.monitorOperlogExport, params);

//获取数据字典类型列表
export const getDictTypeList = (params) => defHttp.post({ url: Api.getDictTypeList, params });
//获取数据字典类型详情
export const getDictTypeById = (Id) => defHttp.get({ url: Api.getDictTypeById + Id });
//删除数据字典类型
export const deleteDictType = (Id) => defHttp.delete({ url: Api.getDictTypeById + Id });
//刷新字典缓存
export const getDictTypeRefreshCache = () => defHttp.delete({ url: Api.getDictTypeRefreshCache });
//更新或新增数据字典类型
export const upOrAddDictType = (method, params) => defHttp[method]({ url: Api.upOrAddDictType, params });
//导出数据字典类型
export const dictTypeExport = (params) => exportExcel(Api.dictTypeExport, params);

export const getDictDataTypeById = (Id) => defHttp.get({ url: Api.getDictDataTypeById + Id });
//获取字典选择框列表
export const getDictTypeOptionselect = () => defHttp.get({ url: Api.getDictTypeOptionselect });
//获取数据字典类型数据项列表
export const getDictDataList = (params) => defHttp.post({ url: Api.getDictDataList, params });
//获取数据字典类型数据项详情
export const getDictDataById = (Id) => defHttp.get({ url: Api.getDictDataById + Id });
//删除数据字典类型数据项
export const deleteDictData = (Id) => defHttp.delete({ url: Api.getDictDataById + Id });
//更新或新增数据字典类型数据项
export const upOrAddDictData = (method, params) => defHttp[method]({ url: Api.upOrAddDictData, params });
//导出数据字典类型数据项
export const dictDataExport = (params) => exportExcel(Api.dictDataExport, params);
//获取登录日志
export const getLoginLogList = (params) => defHttp.post({ url: Api.getLoginLogList, params });
//清空登录日志
export const cleanLoginLog = () => defHttp.delete({ url: Api.cleanLoginLog });

//删除登录日志
export const deleteLoginLog = (infoId) => defHttp.delete({ url: Api.deleteLoginLogById + infoId });
//导出登录日志
export const exportLoginLog = (params) => exportExcel(Api.exportLoginLog, params);
//获取文件列表
export const getFileInfoList = (params) => defHttp.post({ url: Api.getFileInfoList, params });
//删除文件
export const deleteOssById = (id) => defHttp.delete({ url: Api.deleteOssById + id });

//上传文件
export const ossUpload = (data, onUploadProgress?) =>
  defHttp.post(
    {
      url: Api.ossUpload,
      data,
      headers: formHeader,
      onUploadProgress,
    },
    {
      isTransformResponse: false,
    },
  );
//下载文件
export const ossDownload = (id) =>
  defHttp.get(
    {
      url: Api.ossDownload + id,
      responseType: 'blob',
    },
    {
      isTransformResponse: false,
    },
  );
//获取oss配置列表
export const getOssConfigList = (params) => defHttp.post({ url: Api.getOssConfigList, params });
//设置oss配置状态
export const setOssConfigStatus = (params) => defHttp.put({ url: Api.setOssConfigStatus, params });
//删除oss配置
export const deleteOssConfig = (id) => defHttp.delete({ url: Api.deleteOssCOnfigByid + id });
//根据oss配置id获取详细信息
export const getOssConfigById = (id) => defHttp.get({ url: Api.getOssConfigById + id });
//更新或新增oss配置
export const upOrAddOssConfig = (method, params) => defHttp[method]({ url: Api.upOrAddOssConfig, params });

//获取参数配置列表
export const getConfigList = (params) => defHttp.post({ url: Api.getConfigList, params });
//根据参数编号获取详细信息
export const getConfigById = (Id) => defHttp.get({ url: Api.getConfigById + Id });
//删除参数配置
export const deleteConfig = (Id) => defHttp.delete({ url: Api.getConfigById + Id });
//刷新参数配置
export const getConfigRefreshCache = () => defHttp.delete({ url: Api.getConfigRefreshCache });
//更新或新增参数配置
export const upOrAddConfig = (method, params) => defHttp[method]({ url: Api.upOrAddConfig, params });
//导出参数配置
export const configExport = (params) => exportExcel(Api.configExport, params);
//根据参数键名查询参数值
export const getConfigConfigKey = (params) => defHttp.get({ url: Api.getConfigConfigKey + params });

//获取岗位列表
export const getPostList = (params) => defHttp.post({ url: Api.getPostList, params });
//根据岗位id获取详细信息
export const getPostById = (postId) => defHttp.get({ url: Api.getPostById + postId });
//新增|更新岗位
export const upOrAddPost = (method: string, params) => defHttp[method]({ url: Api.upOrAddPost, params });
//删除岗位
export const deletePost = (id) => defHttp.delete({ url: Api.deletePostById + id });
//导出岗位列表
export const exportPostList = (params) => exportExcel(Api.exportPostList, params);

//获取公告列表
export const getNoticeList = (params) => defHttp.post({ url: Api.getNoticeList, params });
//根据公告id获取详细信息
export const getNoticeById = (postId) => defHttp.get({ url: Api.getNoticeById + postId });
//新增|更新公告
export const upOrAddNotice = (method: string, params) => defHttp[method]({ url: Api.upOrAddNotice, params });
//删除公告
export const deleteNotice = (id) => defHttp.delete({ url: Api.deleteNoticeById + id });
//查询基础自控数据管理列表
export const controlDataList = (params) => defHttp.post({ url: Api.controlDataList, params });
export const controlDataListAll = (params?) => defHttp.post({ url: Api.controlDataListAll, params });
//获取基础自控数据管理详细信息
export const getControlDataById = (postId) => defHttp.get({ url: Api.getControlDataById + postId });
//删除基础自控数据管理
export const deleteControlData = (id) => defHttp.post({ url: Api.getControlDataById + id });
//新增基础自控数据管理
export const addControlData = (params) => defHttp.post({ url: Api.addControlData, params });
//修改基础自控数据管理
export const editControlData = (params) => defHttp.post({ url: Api.editControlData, params });

//查询基础自控数据管理列表-属性管理
export const controlDataFieldsList = (params) => defHttp.post({ url: Api.controlDataFieldsList, params });

export const controlDataFieldsListAll = (params?) => defHttp.post({ url: Api.controlDataFieldsListAll, params });
//获取基础自控数据管理详细信息-属性管理
export const getControlDataFieldsById = (postId) => defHttp.get({ url: Api.getControlDataFieldsById + postId });
//删除基础自控数据管理-属性管理
export const deleteControlDataFields = (id) => defHttp.post({ url: Api.getControlDataFieldsById + id });
//新增基础自控数据管理-属性管理
export const addControlDataFields = (params) => defHttp.post({ url: Api.addControlDataFields, params });
//修改基础自控数据管理-属性管理
export const editControlDataFields = (params) => defHttp.post({ url: Api.editControlDataFields, params });

//查询消息模板列表
export const getMsgTemplateList = (params) => defHttp.post({ url: Api.getMsgTemplateList, params });
//查询消息模板列表
export const getMsgTemplateById = (params) => defHttp.get({ url: Api.getMsgTemplateById, params });
//新增消息模板
export const addMsgTemplate = (params) => defHttp.post({ url: Api.addMsgTemplate, params });
//新增消息模板
export const editMsgTemplate = (params) => defHttp.post({ url: Api.editMsgTemplate, params });
//删除消息模板
export const deleteMsgTemplate = (params) => defHttp.post({ url: Api.deleteMsgTemplate, params });
//导出消息模板列表
export const exportMsgTemplate = (params) => exportExcel(Api.exportMsgTemplate, params);
//消息模板测试 详情
export const messageTemplateQueryTest = (id) => defHttp.get({ url: Api.messageTemplateQueryTest + id });
//模板发送测试
export const messageTemplateTest = (params) => defHttp.post({ url: Api.messageTemplateTest, params });

//获取app列表
export const getVersionList = (params) =>
  defHttp.post({
    url: Api.getVersionList,
    params,
  });
//删除app
export const deleteVersionById = (params) => defHttp.post({ url: Api.deleteVersionById, params });
//新增app
export const addVersion = (params) => defHttp.post({ url: Api.addVersion, params });
//修改app
export const editVersion = (params) => defHttp.post({ url: Api.editVersion, params });
//获取app详细信息
export const getVersionById = (params) => defHttp.get({ url: Api.getVersionById, params });

//查询工作台配置列表
export const getWorkstationSetList = (params) => defHttp.post({ url: Api.getWorkstationSetList, params });
//新增工作台配置
export const addWorkstationSet = (params) => defHttp.post({ url: Api.addWorkstationSet, params });
//删除工作台配置
export const deleteWorkstationSet = (params) => defHttp.post({ url: Api.deleteWorkstationSet, params });
//修改工作台配置
export const editWorkstationSet = (params) => defHttp.post({ url: Api.editWorkstationSet, params });
//获取工作台配置详细信息
export const getWorkstationSetById = (params) => defHttp.get({ url: Api.getWorkstationSetById, params });
//导出工作台配置列表
export const exportWorkstationSet = (params) => exportExcel(Api.exportWorkstationSet, params);

//查询系统基础设置天内次数设置列表
export const baseTimesList = (params) => defHttp.post({ url: Api.baseTimesList, params });
//获取系统基础设置天内次数设置详细信息
export const baseTimesById = (Id) => defHttp.get({ url: Api.baseTimesById + Id });
//删除系统基础设置天内次数设置
export const deleteBaseTimes = (Id) => defHttp.post({ url: Api.baseTimesById + Id });
//新增系统基础设置天内次数设置
export const addBaseTimes = (params) => defHttp.post({ url: Api.addBaseTimes, params });
//修改系统基础设置天内次数设置
export const editBaseTimes = (params) => defHttp.post({ url: Api.editBaseTimes, params });
//获取系统基础设置频次设置列表
export const getBaseFrequencyList = (params) => defHttp.post({ url: Api.getBaseFrequencyList, params });

//获取系统基础设置频次设置详情
export const getBaseFrequencyById = (params) => defHttp.get({ url: Api.getBaseFrequencyById, params });

//删除系统基础设置频次设置
export const deleteBaseFrequencyById = (params) => defHttp.post({ url: Api.deleteBaseFrequencyById, params });

//修改系统基础设置频次设置
export const updateBaseFrequency = (params) => defHttp.post({ url: Api.updateBaseFrequency, params });

//新增系统基础设置频次设置
export const addBaseFrequency = (params) => defHttp.post({ url: Api.addBaseFrequency, params });

// 查询工单事件上报列表
export const getEventOrdertList = (params) => defHttp.post({ url: Api.getEventOrdertList, params });

// 查询工单事件上报详情
export const getEventOrderById = (params) => defHttp.get({ url: Api.getEventOrderById, params });

// 删除工单事件上报
export const deleteEventOrderById = (params) => defHttp.post({ url: Api.deleteEventOrderById, params });

// 导出工单事件上报列表
export const exportEventOrder = (params) => exportExcel(Api.exportEventOrder, params);

//获取系统基础设置频次执行实例列表
export const getBaseFrequencyInstanceList = (params) => defHttp.post({ url: Api.getBaseFrequencyInstanceList, params });

//获取系统基础设置频次执行实例详情
export const getBaseFrequencyInstanceById = (params) => defHttp.get({ url: Api.getBaseFrequencyInstanceById, params });
//删除系统基础设置频次执行实例
export const deleteBaseFrequencyInstanceById = (params) =>
  defHttp.post({ url: Api.deleteBaseFrequencyInstanceById, params });
