import { defHttp } from '@/utils/http/axios';

enum Api {
  //模型
  getModelList = '/model/model/list',
  updateModel = '/model/model/update',
  getModelServiceList = '/model/model/service/list',

  //模型功能配置
  getModelFunctionList = '/model/function/list',
  addModelFunction = '/model/function',
  getModelFunctionById = '/model/function/{id}',
  editModelFunction = '/model/function/edit',
  deleteModelFunction = '/model/function/delete/{ids}',
  clearModelFunctionCache = '//model/function/rm/cache/{ids}',
  useModelFunction = '/model/function/useModelFunction/{key}',
}

//模型客户端列表
export const getModelList = (params) => defHttp.get({ url: Api.getModelList, params });

//修改模型客户端
export const updateModel = (params) => defHttp.post({ url: Api.updateModel, params });

//返回所有服务名
export const getModelServiceList = (params) => defHttp.get({ url: Api.getModelServiceList, params });

//查询模型功能配置列表
export const getModelFunctionList = (params) => defHttp.post({ url: Api.getModelFunctionList, params });

//获取排班组详细信息
export const addModelFunction = (params) => defHttp.post({ url: Api.addModelFunction, params });

//获取模型功能配置详细信息
export const getModelFunctionById = (params) => defHttp.post({ url: Api.getModelFunctionById, params });

//修改模型功能配置
export const editModelFunction = (params) => defHttp.post({ url: Api.editModelFunction, params });

//删除模型功能配置
export const deleteModelFunction = (params) => defHttp.post({ url: Api.deleteModelFunction, params });

//删除模型功能配置
export const clearModelFunctionCache = (params) => defHttp.post({ url: Api.clearModelFunctionCache, params });

export const useModelFunction = (params) =>
  defHttp.post({
    url: params.test
      ? `${Api.useModelFunction}?test={test}${params.count ? '&count={count}' : ''}`
      : Api.useModelFunction,
    params,
  });
