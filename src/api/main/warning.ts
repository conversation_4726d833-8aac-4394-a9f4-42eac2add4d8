import { defHttp } from '@/utils/http/axios';

enum Api {
  //预警异常值规则
  getRuleConfigList = '/warn/rule/config/list',
  getRuleConfigById = '/warn/rule/config/{id}',
  deleteRuleConfigById = '/warn/rule/config/remove/{id}',
  isEnableRuleConfig = '/warn/rule/config/switch/{id}/{enableFlag}',
  updateRuleConfig = '/warn/rule/config/edit',
  addRuleConfig = '/warn/rule/config',
  isEnableDayAndNightRuleConfig = '/warn/rule/config/dayAndNight/manage/switch/{isEnable}',
  manageDayAndNightRuleConfig = '/warn/rule/config/dayAndNight/manage',
  queryDayAndNightRuleConfig = '/warn/rule/config/dayAndNight/manage/query',
  //预警管理-通知规则
  noticeCommRuleList = '/system/noticeCommRule/list', //查询预警通知公共规则列表
  noticeCommRuleById = '/system/noticeCommRule/{id}', //获取预警通知公共规则详细信息
  addNoticeCommRule = '/system/noticeCommRule', //新增预警通知公共规则
  editNoticeCommRule = '/system/noticeCommRule/edit', //修改预警通知公共规则
  enableFlagNoticeCommRule = '/system/noticeCommRule/{id}/{enableFlag}', //开启&关闭预警通知公共规则
  noticeCommRuleUnlinkList = '/system/noticeCommRule/unlinkUser/{id}', //预警通知公共规则查未关联用户-分页
  noticeCommRuleLinkList = '/system/noticeCommRule/linkUser/{id}', //预警通知公共规则已关联用户-分页
  noticeCommRuleUnLinkUser = '/system/noticeCommRule/linkUser/unlink/', //规则批量解绑消息接收用户
  noticeCommRuleLinkUser = '/system/noticeCommRule/linkUser/link/', //规则批量添加消息接收用户
  noticeCommRuleLinkUserEdit = '/system/noticeCommRule/linkUser/edit/', //修改接收用户接收消息类
  getWarnInfoList = '/warn/info/list',
  getWarnInfoById = '/warn/info/{id}',
}

//获取预警异常值规则列表
export const getRuleConfigList = (params) => defHttp.post({ url: Api.getRuleConfigList, params });

//获取预警异常值规则详情
export const getRuleConfigById = (params) => defHttp.get({ url: Api.getRuleConfigById, params });

//删除预警异常值规则
export const deleteRuleConfigById = (params) =>
  defHttp.post({ url: Api.deleteRuleConfigById, params });

//修改预警异常值规则
export const updateRuleConfig = (params) => defHttp.post({ url: Api.updateRuleConfig, params });

//新增预警异常值规则
export const addRuleConfig = (params) => defHttp.post({ url: Api.addRuleConfig, params });

//开启或关闭规则
export const isEnableRuleConfig = (params) => defHttp.post({ url: Api.isEnableRuleConfig, params });

//预警日夜规则开启&关闭
export const isEnableDayAndNightRuleConfig = (params) =>
  defHttp.post({ url: Api.isEnableDayAndNightRuleConfig, params });

//日夜规则维护
export const manageDayAndNightRuleConfig = (params) =>
  defHttp.post({ url: Api.manageDayAndNightRuleConfig, params });

//日夜规则详情
export const queryDayAndNightRuleConfig = () =>
  defHttp.get({ url: Api.queryDayAndNightRuleConfig });

//查询预警通知公共规则列表
export const noticeCommRuleList = (params) => defHttp.post({ url: Api.noticeCommRuleList, params });
//获取预警通知公共规则详细信息
export const noticeCommRuleById = (params) => defHttp.get({ url: Api.noticeCommRuleById, params });
//删除预警通知公共规则
export const deleteNoticeCommRule = (params) =>
  defHttp.post({ url: Api.noticeCommRuleById, params });
//修改预警通知公共规则
export const editNoticeCommRule = (params) => defHttp.post({ url: Api.editNoticeCommRule, params });
//新增预警通知公共规则
export const addNoticeCommRule = (params) => defHttp.post({ url: Api.addNoticeCommRule, params });
//开启&关闭预警通知公共规则
export const enableFlagNoticeCommRule = (params) =>
  defHttp.post({ url: Api.enableFlagNoticeCommRule, params });

//预警通知公共规则查未关联用户-分页
export const noticeCommRuleUnlinkList = (params) =>
  defHttp.post({ url: Api.noticeCommRuleUnlinkList, params });
//预警通知公共规则已关联用户-分页
export const noticeCommRuleLinkList = (params) =>
  defHttp.post({ url: Api.noticeCommRuleLinkList, params });
//规则批量解绑消息接收用户
export const noticeCommRuleUnLinkUser = (id, params) =>
  defHttp.post({ url: Api.noticeCommRuleUnLinkUser + id, params });
//规则批量添加消息接收用户
export const noticeCommRuleLinkUser = (id, params) =>
  defHttp.post({ url: Api.noticeCommRuleLinkUser + id, params });
//修改接收用户接收消息类
export const noticeCommRuleLinkUserEdit = (id, params) =>
  defHttp.post({ url: Api.noticeCommRuleLinkUserEdit + id, params });

//查询预警信息列表
export const getWarnInfoList = (params) => defHttp.post({ url: Api.getWarnInfoList, params });
//查询预警信息列表
export const getWarnInfoById = (params) => defHttp.post({ url: Api.getWarnInfoById, params });
