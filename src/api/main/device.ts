import { defHttp } from '@/utils/http/axios';
import { exportExcel } from '../baseApi';

enum Api {
  //设备类型
  getCategoriesList = '/system/categories/list',
  getCategoriesById = '/system/categories/{id}',
  deleteCategoriesById = '/system/categories/{id}',
  updateCategories = '/system/categories/edit',
  addCategories = '/system/categories',
  //设备仓库
  getWarehousesList = '/system/warehouses/list',
  getWarehousesById = '/system/warehouses/{id}',
  deleteWarehousesById = '/system/warehouses/{id}',
  updateWarehouses = '/system/warehouses/edit',
  addWarehouses = '/system/warehouses',
  getWarehousesByWareHouseId = '/system/warehouses/getMaterials/{id}',
  getWarehousesByMaterialId = '/device/materials/getWarehousesInfo/{id}',
  //设备安装地点
  getInstallationLocationsList = '/system/installationLocations/list',
  getInstallationLocationsById = '/system/installationLocations/{id}',
  deleteInstallationLocationsById = '/system/installationLocations/{id}',
  updateInstallationLocations = '/system/installationLocations/edit',
  addInstallationLocations = '/system/installationLocations',
  //设备供应商
  getSuppliersList = '/system/suppliers/list',
  getSuppliersById = '/system/suppliers/{id}',
  deleteSuppliersById = '/system/suppliers/{id}',
  updateSuppliers = '/system/suppliers/edit',
  addSuppliers = '/system/suppliers',
  //备件单位
  getSparePartsUnitsList = '/system/sparePartsUnits/list',
  getSparePartsUnitsById = '/system/sparePartsUnits/{id}',
  deleteSparePartsUnitsById = '/system/sparePartsUnits/{id}',
  updateSparePartsUnits = '/system/sparePartsUnits/edit',
  addSparePartsUnits = '/system/sparePartsUnits',
  //设备物料
  getMaterialsList = '/device/materials/list',
  getMaterialsById = '/device/materials/{id}',
  deleteMaterialsById = '/device/materials/{id}',
  updateMaterials = '/device/materials/edit',
  addMaterials = '/device/materials',
  //设备入库
  getAddInventoryList = '/device/addInventory/list',
  getAddInventoryById = '/device/addInventory/{id}',
  addAddInventory = '/device/addInventory',
  //出库
  getStockOutList = '/device/stockOut/list',
  getStockOutById = '/device/stockOut/{id}',
  addStockOut = '/device/stockOut',
  getStockOutInfoList = '/device/stockOut/inventoryAll',
  //设备维修
  getDeviceMaintenanceList = '/system/DeviceMaintenanceWD/list',
  addDeviceMaintenance = '/system/DeviceMaintenanceWD',
  eidtDeviceMaintenance = '/system/DeviceMaintenanceWD/edit',
  deleteDeviceMaintenance = '/system/DeviceMaintenanceWD/{ids}',
  exportDeviceMaintenance = '/system/DeviceMaintenanceWD/export',
  getDeviceMaintenanceById = '/system/DeviceMaintenanceWD/{id}',
  //设备台账
  deviceDataInfoList = '/device/dataInfo/list', //查询设备基础信息列表
  deviceDataInfoById = '/device/dataInfo/', //获取设备基础信息详细信息
  addDeviceDataInfo = '/device/dataInfo', //新增设备基础信息
  updateDeviceDataInfo = '/device/dataInfo/edit', //修改设备基础信息
  //设备-点检标准
  getInspectionStandardsList = '/device/inspectionStandards/list',
  getInspectionStandardsById = '/device/inspectionStandards/{id}',
  deleteInspectionStandardsById = '/device/inspectionStandards/{id}',
  updateInspectionStandards = '/device/inspectionStandards/edit',
  addInspectionStandards = '/device/inspectionStandards',
  //设备点检单
  getInspectionRecordsList = '/device/inspectionRecords/list',
  getInspectionRecordsById = '/device/inspectionRecords/{id}',
  deleteInspectionRecordsById = '/device/inspectionRecords/{id}',
  updateInspectionRecords = '/device/inspectionRecords/edit',
  addInspectionRecords = '/device/inspectionRecords',
  //设备保养项模版
  careTemplatesList = '/device/careTemplates/list', //查询设备保养项模版列表
  careTemplatesById = '/device/careTemplates/', //获取设备保养项模版详细信息
  addCareTemplates = '/device/careTemplates', //新增设备保养项模版
  updateCareTemplates = '/device/careTemplates/edit', //修改设备保养项模版
  exportCareTemplates = '/device/careTemplates/export', //导出设备保养项模版列表
  //设备保养单
  orderDeviceUpkeepList = '/device/orderDeviceUpkeep/list', //查询工单设备保养单列表
  orderDeviceUpkeepById = '/device/orderDeviceUpkeep/', //获取设备保养项模版详细信息
  //设备保养计划
  upkeepPlanList = '/system/upkeepPlan/list', //查询设备保养计划列表
  upkeepPlanById = '/system/upkeepPlan/', //获取设备保养计划详细信息
  addUpkeepPlan = '/system/upkeepPlan', //新增设备保养计划
  updateUpkeepPlan = '/system/upkeepPlan/edit', //修改设备保养计划
  upkeepPlanEnable = '/system/upkeepPlan/enable/{id}/{enable}', //开启&关闭保养计划
  exportUpkeepPlan = '/system/upkeepPlan/export', //导出设备保养计划列表

  //设备巡检计划
  getInspectionPlanList = '/device/inspectionPlan/list',
  getInspectionPlanById = '/device/inspectionPlan/{id}',
  deleteInspectionPlanById = '/device/inspectionPlan/{id}',
  updateInspectionPlan = '/device/inspectionPlan/edit',
  addInspectionPlan = '/device/inspectionPlan',
  updateInspectionEnable = '/device/inspectionPlan/enable/{id}/{enable}',
  //设备巡检任务
  getInspectionTaskList = '/device/inspectionTask/list',
  getInspectionTaskById = '/device/inspectionTask/{id}',

  //设备保养单日历
  orderDeviceUpkeepCalendarList = '/device/orderDeviceUpkeep/calendar/list',
}

//获取设备分类清单列表
export const getCategoriesList = (params) => defHttp.post({ url: Api.getCategoriesList, params });

//获取设备分类清单详情
export const getCategoriesById = (params) => defHttp.get({ url: Api.getCategoriesById, params });

//删除设备分类清单
export const deleteCategoriesById = (params) => defHttp.post({ url: Api.deleteCategoriesById, params });

//修改设备分类清单
export const updateCategories = (params) => defHttp.post({ url: Api.updateCategories, params });

//新增设备分类清单
export const addCategories = (params) => defHttp.post({ url: Api.addCategories, params });

//获取设备仓库列表
export const getWarehousesList = (params) => defHttp.post({ url: Api.getWarehousesList, params });

//获取设备仓库详情
export const getWarehousesById = (params) => defHttp.get({ url: Api.getWarehousesById, params });

//删除设备仓库
export const deleteWarehousesById = (params) => defHttp.post({ url: Api.deleteWarehousesById, params });

//修改设备仓库
export const updateWarehouses = (params) => defHttp.post({ url: Api.updateWarehouses, params });

//新增设备仓库
export const addWarehouses = (params) => defHttp.post({ url: Api.addWarehouses, params });
//根据物料获取仓库物料信息
export const getWarehousesByMaterialId = (params) => defHttp.post({ url: Api.getWarehousesByMaterialId, params });
export const getWarehousesByWareHouseId = (params) => defHttp.post({ url: Api.getWarehousesByWareHouseId, params });
//获取设备安装地点列表
export const getInstallationLocationsList = (params) => defHttp.post({ url: Api.getInstallationLocationsList, params });

//获取设备安装地点详情
export const getInstallationLocationsById = (params) => defHttp.get({ url: Api.getInstallationLocationsById, params });

//删除设备安装地点
export const deleteInstallationLocationsById = (params) =>
  defHttp.post({ url: Api.deleteInstallationLocationsById, params });

//修改设备安装地点
export const updateInstallationLocations = (params) => defHttp.post({ url: Api.updateInstallationLocations, params });

//新增设备安装地点
export const addInstallationLocations = (params) => defHttp.post({ url: Api.addInstallationLocations, params });

//获取设备供应商列表
export const getSuppliersList = (params) => defHttp.post({ url: Api.getSuppliersList, params });

//获取设备供应商详情
export const getSuppliersById = (params) => defHttp.get({ url: Api.getSuppliersById, params });

//删除设备供应商
export const deleteSuppliersById = (params) => defHttp.post({ url: Api.deleteSuppliersById, params });

//修改设备供应商
export const updateSuppliers = (params) => defHttp.post({ url: Api.updateSuppliers, params });

//新增设备供应商
export const addSuppliers = (params) => defHttp.post({ url: Api.addSuppliers, params });

//获取备件单位列表
export const getSparePartsUnitsList = (params) => defHttp.post({ url: Api.getSparePartsUnitsList, params });

//获取备件单位详情
export const getSparePartsUnitsById = (params) => defHttp.get({ url: Api.getSparePartsUnitsById, params });

//删除备件单位
export const deleteSparePartsUnitsById = (params) => defHttp.post({ url: Api.deleteSparePartsUnitsById, params });

//修改备件单位
export const updateSparePartsUnits = (params) => defHttp.post({ url: Api.updateSparePartsUnits, params });

//新增备件单位
export const addSparePartsUnits = (params) => defHttp.post({ url: Api.addSparePartsUnits, params });

//获取设备物料列表
export const getMaterialsList = (params) => defHttp.post({ url: Api.getMaterialsList, params });

//获取设备物料详情
export const getMaterialsById = (params) => defHttp.get({ url: Api.getMaterialsById, params });

//删除设备物料
export const deleteMaterialsById = (params) => defHttp.post({ url: Api.deleteMaterialsById, params });

//修改设备物料
export const updateMaterials = (params) => defHttp.post({ url: Api.updateMaterials, params });

//新增设备物料
export const addMaterials = (params) => defHttp.post({ url: Api.addMaterials, params });

//获取设备入库总列表
export const getAddInventoryList = (params) => defHttp.post({ url: Api.getAddInventoryList, params });

//获取设备入库总详情
export const getAddInventoryById = (params) => defHttp.get({ url: Api.getAddInventoryById, params });

//新增设备入库总
export const addAddInventory = (params) => defHttp.post({ url: Api.addAddInventory, params });
//获取设备出库列表
export const getStockOutList = (params) => defHttp.post({ url: Api.getStockOutList, params });
//获取设备出库详情
export const getStockOutById = (params) => defHttp.get({ url: Api.getStockOutById, params });
//新增设备出库
export const addStockOut = (params) => defHttp.post({ url: Api.addStockOut, params });
//获取物料库存信息
export const getStockOutInfoList = (params) => defHttp.post({ url: Api.getStockOutInfoList, params });

//查询设备维修工单列表
export const getDeviceMaintenanceList = (params) => defHttp.post({ url: Api.getDeviceMaintenanceList, params });

//新增设备维修工单
export const addDeviceMaintenance = (params) => defHttp.post({ url: Api.addDeviceMaintenance, params });

//修改设备维修工单
export const eidtDeviceMaintenance = (params) => defHttp.post({ url: Api.eidtDeviceMaintenance, params });

//删除设备维修工单
export const deleteDeviceMaintenance = (params) => defHttp.post({ url: Api.deleteDeviceMaintenance, params });

//获取设备维修工单详细信息
export const getDeviceMaintenanceById = (params) => defHttp.get({ url: Api.getDeviceMaintenanceById, params });

//导出设备维修工单列表
export const exportDeviceMaintenance = (params) => exportExcel(Api.exportDeviceMaintenance, params);

//查询设备基础信息列表
export const deviceDataInfoList = (params) => defHttp.post({ url: Api.deviceDataInfoList, params });
//获取设备基础信息详细信息
export const deviceDataInfoById = (Id) => defHttp.get({ url: Api.deviceDataInfoById + Id });
//删除设备基础信息
export const deleteDeviceDataInfo = (Id) => defHttp.post({ url: Api.deviceDataInfoById + Id });
//新增设备基础信息
export const addDeviceDataInfo = (params) => defHttp.post({ url: Api.addDeviceDataInfo, params });
//修改设备基础信息
export const updateDeviceDataInfo = (params) => defHttp.post({ url: Api.updateDeviceDataInfo, params });

//获取设备-点检标准列表
export const getInspectionStandardsList = (params) => defHttp.post({ url: Api.getInspectionStandardsList, params });

//获取设备-点检标准详情
export const getInspectionStandardsById = (params) => defHttp.get({ url: Api.getInspectionStandardsById, params });

//删除设备-点检标准
export const deleteInspectionStandardsById = (params) =>
  defHttp.post({ url: Api.deleteInspectionStandardsById, params });

//修改设备-点检标准
export const updateInspectionStandards = (params) => defHttp.post({ url: Api.updateInspectionStandards, params });

//新增设备-点检标准
export const addInspectionStandards = (params) => defHttp.post({ url: Api.addInspectionStandards, params });
//获取设备点检单列表
export const getInspectionRecordsList = (params) => defHttp.post({ url: Api.getInspectionRecordsList, params });

//获取设备点检单详情
export const getInspectionRecordsById = (params) => defHttp.get({ url: Api.getInspectionRecordsById, params });

//删除设备点检单
export const deleteInspectionRecordsById = (params) => defHttp.post({ url: Api.deleteInspectionRecordsById, params });

//修改设备点检单
export const updateInspectionRecords = (params) => defHttp.post({ url: Api.updateInspectionRecords, params });

//新增设备点检单
export const addInspectionRecords = (params) => defHttp.post({ url: Api.addInspectionRecords, params });

//查询设备保养项模版列表
export const careTemplatesList = (params) => defHttp.post({ url: Api.careTemplatesList, params });
//获取设备保养项模版详细信息
export const careTemplatesById = (Id) => defHttp.get({ url: Api.careTemplatesById + Id });
//删除设备保养项模版
export const deleteCareTemplates = (Id) => defHttp.post({ url: Api.careTemplatesById + Id });
//新增设备保养项模版
export const addCareTemplates = (params) => defHttp.post({ url: Api.addCareTemplates, params });
//修改设备保养项模版
export const updateCareTemplates = (params) => defHttp.post({ url: Api.updateCareTemplates, params });
//导出设备保养项模版列表
export const exportCareTemplates = (params) => exportExcel(Api.exportCareTemplates, params);

//查询工单设备保养单列表
export const orderDeviceUpkeepList = (params) => defHttp.post({ url: Api.orderDeviceUpkeepList, params });
//获取设备保养项模版详细信息
export const orderDeviceUpkeepById = (Id) => defHttp.get({ url: Api.orderDeviceUpkeepById + Id });

//查询设备保养计划列表
export const upkeepPlanList = (params) => defHttp.post({ url: Api.upkeepPlanList, params });
//获取设备保养计划详细信息
export const upkeepPlanById = (Id) => defHttp.get({ url: Api.upkeepPlanById + Id });
//删除设备保养计划
export const deleteUpkeepPlan = (Id) => defHttp.post({ url: Api.upkeepPlanById + Id });
//新增设备保养计划
export const addUpkeepPlan = (params) => defHttp.post({ url: Api.addUpkeepPlan, params });
//修改设备保养计划
export const updateUpkeepPlan = (params) => defHttp.post({ url: Api.updateUpkeepPlan, params });
//开启&关闭保养计划
export const upkeepPlanEnable = (params) => defHttp.post({ url: Api.upkeepPlanEnable, params });
//导出设备保养项模版列表
export const exportUpkeepPlan = (params) => exportExcel(Api.exportUpkeepPlan, params);

//获取点巡检计划列表
export const getInspectionPlanList = (params) => defHttp.post({ url: Api.getInspectionPlanList, params });

//获取点巡检计划详情
export const getInspectionPlanById = (params) => defHttp.get({ url: Api.getInspectionPlanById, params });

//删除点巡检计划
export const deleteInspectionPlanById = (params) => defHttp.post({ url: Api.deleteInspectionPlanById, params });

//修改点巡检计划
export const updateInspectionPlan = (params) => defHttp.post({ url: Api.updateInspectionPlan, params });

//新增点巡检计划
export const addInspectionPlan = (params) => defHttp.post({ url: Api.addInspectionPlan, params });
//修改点巡检计划状态
export const updateInspectionEnable = (params) => defHttp.post({ url: Api.updateInspectionEnable, params });
//获取设备巡检任务列表
export const getInspectionTaskList = (params) => defHttp.post({ url: Api.getInspectionTaskList, params });

//获取设备巡检任务详情
export const getInspectionTaskById = (params) => defHttp.post({ url: Api.getInspectionTaskById, params });

//获取设备巡检任务详情
export const orderDeviceUpkeepCalendarList = (params) =>
  defHttp.post({ url: Api.orderDeviceUpkeepCalendarList, params });
