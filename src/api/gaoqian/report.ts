import { defHttp } from '@/utils/http/axios';

enum Api {
  getLinqiLeaseInfo = '/gzf/leaseInfo/linqi/list',

  getLinqiStatistics = '/gzf/leaseInfo/linqi/statistics',

  getKongzhiStatistics = '/gzf/report/kongzhi',

  getNewTaskTotal = '/gzf/report/newTaskTotal',

  getHouseTotal = '/gzf/report/houseTotal',

  getArrearsTotal = '/gzf/report/arrearsTotal',

  getAddPaymentTotal = '/gzf/report/addPaymentTotal',

  getAddLeaseTotal = '/gzf/report/addLeaseTotal',

  getPaymentByType = '/gzf/report/payment/typeSum',

  getSameTotal = '/gzf/report/sameTotal',
  getTaskTotal = '/gzf/report/my/task/total',
  //收款类别占比
  getPaymentType = '/payment/paymentSlips/collectionType',
  //统计房屋年限
  getHouseYear = '/gzf/report/statisticsHousingYears',
  //统计--费用统计看板
  getCostStatistics = '/gzf/statistics/costStatistics',
  //统计--欠费统计看板
  getArrearsStatistics = '/gzf/statistics/arrearsStatistics',
  //动态获取收费项
  getChargeItem = '/gzf/statistics/chargingRelationshipList',
  //数据统计
  getStatistics = '/gzf/report/numericalDataStatistics',
  //按照支付方式统计
  getPaymentByPayType = '/gzf/statistics/paymentMethodStatistics',

  //获取日月报表
  getDailyReport = '/gzf/statistics/areaDataStatistics',
  //收费明细日报表
  getDailyChargeReport = '/payment/paymentSlipsDetails/queryPaymentSlipsDetails',
  //操作员收费统计表
  getOperatorChargeReport = '/payment/paymentSlipsDetails/queryOperatorPaymentReport',
  //年度汇总
  getYearlyReport = '/payment/paymentSlipsDetails/queryChargingItemYearReport',
}
// 获取临期合同信息
export const getLinqiLeaseInfo = (params) => defHttp.post({ url: Api.getLinqiLeaseInfo, params });
// 获取临期统计信息
export const getLinqiStatistics = (params?) => defHttp.post({ url: Api.getLinqiStatistics, params });
// 获取空置统计信息
export const getKongzhiStatistics = (params?) => defHttp.post({ url: Api.getKongzhiStatistics, params });

// 获取新增任务总数
export const getNewTaskTotal = (params?) => defHttp.post({ url: Api.getNewTaskTotal, params });
// 获取房源总数
export const getHouseTotal = (params?) => defHttp.post({ url: Api.getHouseTotal, params });
// 获取欠费总数
export const getArrearsTotal = (params?) => defHttp.post({ url: Api.getArrearsTotal, params });
// 获取新增收款总数
export const getAddPaymentTotal = (params?) => defHttp.post({ url: Api.getAddPaymentTotal, params });
// 获取新增合同总数
export const getAddLeaseTotal = (params?) => defHttp.post({ url: Api.getAddLeaseTotal, params });
// 获取收款类型统计
export const getPaymentByType = (params?) => defHttp.post({ url: Api.getPaymentByType, params });
//
export const getSameTotal = (params?) => defHttp.post({ url: Api.getSameTotal, params });

// 获取我的任务总数
export const getTaskTotal = (params?) => defHttp.post({ url: Api.getTaskTotal, params });
// 获取收款类别占比
export const getPaymentType = (params?) => defHttp.post({ url: Api.getPaymentType, params });
// 统计房屋年限
export const getHouseYear = (params?) => defHttp.post({ url: Api.getHouseYear, params });
// 统计--费用统计看板
export const getCostStatistics = (params?) => defHttp.post({ url: Api.getCostStatistics, params });
// 统计--欠费统计看板
export const getArrearsStatistics = (params?) => defHttp.post({ url: Api.getArrearsStatistics, params });
// 动态获取收费项
export const getChargeItem = (params?) => defHttp.post({ url: Api.getChargeItem, params });
// 数据统计
export const getStatistics = (params?) => defHttp.post({ url: Api.getStatistics, params });
// 按照支付方式统计
export const getPaymentByPayType = (params?) => defHttp.post({ url: Api.getPaymentByPayType, params });
// 获取日月报表
export const getDailyReport = (params?) => defHttp.post({ url: Api.getDailyReport, params });
// 收费明细日报表
export const getDailyChargeReport = (params?) => defHttp.post({ url: Api.getDailyChargeReport, params });
// 操作员收费统计表
export const getOperatorChargeReport = (params?) => defHttp.post({ url: Api.getOperatorChargeReport, params });
// 年度汇总
export const getYearlyReport = (params?) => defHttp.post({ url: Api.getYearlyReport, params });
