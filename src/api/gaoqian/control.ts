import { defHttp } from '@/utils/http/axios';

enum Api {
  //缴费记录
  getRentChargeList = '/system/rentCharge/list',
  getRentChargeById = '/system/rentCharge/{id}',
  deleteRentChargeById = '/system/rentCharge/{id}',
  updateRentCharge = '/system/rentCharge/edit',
  addRentCharge = '/system/rentCharge',
  //附加信息
  getHouseAppendInfoList = '/system/houseAppendInfo/list',
  getHouseAppendInfoById = '/system/houseAppendInfo/{id}',
  deleteHouseAppendInfoById = '/system/houseAppendInfo/{id}',
  updateHouseAppendInfo = '/system/houseAppendInfo/edit',
  addHouseAppendInfo = '/system/houseAppendInfo',
  //变更记录
  getHouseBasicInfoLogList = '/system/houseBasicInfoLog/list',
  getHouseBasicInfoLogById = '/system/houseBasicInfoLog/{id}',
  deleteHouseBasicInfoLogById = '/system/houseBasicInfoLog/{id}',
  updateHouseBasicInfoLog = '/system/houseBasicInfoLog/edit',
  addHouseBasicInfoLog = '/system/houseBasicInfoLog',
  getPayMentListByKeyName = '/gzf/leaseInfo/queryLeaseList',
  getPayInfoById = '/gzf/leaseInfo/queryLeaseInfo/{id}',
  goPayment = '/gzf/leaseInfo/goPayment',

  getMaxMonth = '/payment/paymentSlips/queryMaxPaymentMonths/{id}',
  createPayment = '/payment/paymentSlips',
  startRent = '/gzf/domain/rent/sign/v2',
  cancelRent = '/gzf/domain/cancel/sign',
  endRent = '/gzf/domain/rent/third',
  getSigningList = '/gzf/leaseIngFlow/list',
  getSignIngById = '/gzf/domain/signing/{id}',
  getExtensionInfoList = '/system/extensionInfo/list',
  getExtensionInfoById = '/system/extensionInfo/{id}',
  deleteExtensionInfoById = '/system/extensionInfo/revokedApplication/{id}',
  approvedExtensionInfo = '/system/extensionInfo/applicationApproved/{id}',
  updateExtensionInfo = '/system/extensionInfo/edit',
  addExtensionInfo = '/system/extensionInfo',

  addSettlement = '/system/settlementReturn/start/settlementReturn',
  getSettlementInfo = '/system/settlementReturn/settlement/info/{id}',
  getSettlementOrder = '/system/settlementReturn/paymentSlips/{id}',
  addDeposit = '/system/settlementReturn/deposit/{id}',
  addAmount = '/system/settlementReturn/add',
  deleteAmount = '/system/settlementReturn/remove/{leaseId}/{type}',
  deleteDeposit = '/system/settlementReturn/deposit/remove/{id}',

  getLeaseChangeList = '/system/extensionInfo/changeRecords/{id}',

  commitSettlement = '/system/settlementReturn/commit/{id}',

  getAssignmentList = '/system/collectionHouse/list',
  addAssignmentUser = '/system/collectionHouse/addFeeCollector',
  addAssignment = '/system/collectionHouse',
  deleteAssignmentUser = '/system/collectionHouse/removeFeeCollector/{id}',

  getRentCollectionConfigList = '/system/rentCollectionConfig/list',
  getRentCollectionConfigById = '/system/rentCollectionConfig/{id}',
  deleteRentCollectionConfigById = '/system/rentCollectionConfig/delete/{id}',
  setTaskStatus = '/system/rentCollectionConfig/closeTask/{taskId}',
  getCloseTaskMsg = '/system/rentCollectionConfig/closeTaskDetail/{taskId}',
  updateRentCollectionConfig = '/system/rentCollectionConfig/regeneratePayment',
  addRentCollectionConfig = '/system/rentCollectionConfig',

  addTypePayment = '/payment/paymentSlips/add/{id}',

  getLeaseLendList = '/gzf/leaseLend/list',
  getLeaseLendById = '/gzf/leaseLend/{id}',
  deleteLeaseLendById = '/gzf/leaseLend/{id}',
  updateLeaseLend = '/gzf/leaseLend/edit',
  addLeaseLend = '/gzf/leaseLend',
  returnLeaseLend = '/gzf/leaseLend/return/{id}',
  rechargeAdd = '/gzf/recharge/add',
  getRechargeList = '/gzf/recharge/list/{id}',
  //根据合约id获取验房详情
  getCheckHouseInfo = '/gzf/acceptance/{id}',

  //合同续约
  setNewEndTime = '/gzf/domain/renewal/v2',
  //合同中止
  setStopTime = '/gzf/domain/earlytermination',

  //日期修正
  setNewTime = '/gzf/domain/updateStart',

  setNewShowTime = '/gzf/leaseInfo/editShowTime',

  updateLeaseMonthAmount = '/gzf/domain/update/lease/monthAmount/{houseId}/{leaseId}/{monthAmount}',
  //同步租户姓名
  syncTenantName = '/gzf/domain/update/syncName/{houseId}',
}

//获取缴费记录列表
export const getRentChargeList = (params) => defHttp.post({ url: Api.getRentChargeList, params });

//获取缴费记录详情
export const getRentChargeById = (params) => defHttp.post({ url: Api.getRentChargeById, params });

//删除缴费记录
export const deleteRentChargeById = (params) => defHttp.post({ url: Api.deleteRentChargeById, params });

//修改缴费记录
export const updateRentCharge = (params) => defHttp.post({ url: Api.updateRentCharge, params });

//新增缴费记录
export const addRentCharge = (params) => defHttp.post({ url: Api.addRentCharge, params });

//获取房屋附加信息列表
export const getHouseAppendInfoList = (params) => defHttp.post({ url: Api.getHouseAppendInfoList, params });

//获取房屋附加信息详情
export const getHouseAppendInfoById = (params) => defHttp.post({ url: Api.getHouseAppendInfoById, params });

//删除房屋附加信息
export const deleteHouseAppendInfoById = (params) => defHttp.post({ url: Api.deleteHouseAppendInfoById, params });

//修改房屋附加信息
export const updateHouseAppendInfo = (params) => defHttp.post({ url: Api.updateHouseAppendInfo, params });

//新增房屋附加信息
export const addHouseAppendInfo = (params) => defHttp.post({ url: Api.addHouseAppendInfo, params });
//获取变更记录列表
export const getHouseBasicInfoLogList = (params) => defHttp.post({ url: Api.getHouseBasicInfoLogList, params });

//获取变更记录详情
export const getHouseBasicInfoLogById = (params) => defHttp.get({ url: Api.getHouseBasicInfoLogById, params });

//删除变更记录
export const deleteHouseBasicInfoLogById = (params) => defHttp.post({ url: Api.deleteHouseBasicInfoLogById, params });

//修改变更记录
export const updateHouseBasicInfoLog = (params) => defHttp.post({ url: Api.updateHouseBasicInfoLog, params });

//新增变更记录
export const addHouseBasicInfoLog = (params) => defHttp.post({ url: Api.addHouseBasicInfoLog, params });

//获取租金缴费列表
export const getPayMentListByKeyName = (params) => defHttp.post({ url: Api.getPayMentListByKeyName, params });
//获取租金缴费详情
export const getPayInfoById = (params) => defHttp.post({ url: Api.getPayInfoById, params });
//缴费
export const goPayment = (params) => defHttp.post({ url: Api.goPayment, params });

//获取最大缴费月份
export const getMaxMonth = (params) => defHttp.post({ url: Api.getMaxMonth, params });
//新增缴费
export const createPayment = (params) => defHttp.post({ url: Api.createPayment, params });
//开始出租
export const startRent = (params) => defHttp.post({ url: Api.startRent, params });
//取消出租
export const cancelRent = (params) => defHttp.post({ url: Api.cancelRent, params });
//结束出租
export const endRent = (params) => defHttp.post({ url: Api.endRent, params });

export const getSigningList = (params) => defHttp.post({ url: Api.getSigningList, params });
//获取签约详情
export const getSignIngById = (params) => defHttp.post({ url: Api.getSignIngById, params });

//获取延期申请列表
export const getExtensionInfoList = (params) => defHttp.post({ url: Api.getExtensionInfoList, params });

//获取延期申请详情
export const getExtensionInfoById = (params) => defHttp.post({ url: Api.getExtensionInfoById, params });

//删除延期申请
export const deleteExtensionInfoById = (params) => defHttp.post({ url: Api.deleteExtensionInfoById, params });
//审核延期申请
export const approvedExtensionInfo = (params) => defHttp.post({ url: Api.approvedExtensionInfo, params });

//修改延期申请
export const updateExtensionInfo = (params) => defHttp.post({ url: Api.updateExtensionInfo, params });

//新增延期申请
export const addExtensionInfo = (params) => defHttp.post({ url: Api.addExtensionInfo, params });
//新增结算
export const addSettlement = (params) => defHttp.post({ url: Api.addSettlement, params });
//获取结算详情
export const getSettlementInfo = (params) => defHttp.post({ url: Api.getSettlementInfo, params });
//获取结算缴费单
export const getSettlementOrder = (params) => defHttp.post({ url: Api.getSettlementOrder, params });
//新增抵扣押金
export const addDeposit = (params) => defHttp.post({ url: Api.addDeposit, params });
//删除金额
export const deleteAmount = (params) => defHttp.post({ url: Api.deleteAmount, params });

//新增金额
export const addAmount = (params) => defHttp.post({ url: Api.addAmount, params });
//删除押金
export const deleteDeposit = (params) => defHttp.post({ url: Api.deleteDeposit, params });
//获取变更记录
export const getLeaseChangeList = (params) => defHttp.post({ url: Api.getLeaseChangeList, params });
//提交结算
export const commitSettlement = (params) => defHttp.post({ url: Api.commitSettlement, params });
//获取收款人列表
export const getAssignmentList = (params?) => defHttp.post({ url: Api.getAssignmentList, params });
//新增收款人
export const addAssignmentUser = (params) => defHttp.post({ url: Api.addAssignmentUser, params });
//新增收款人
export const addAssignment = (params) => defHttp.post({ url: Api.addAssignment, params });
//删除收款人
export const deleteAssignmentUser = (params) => defHttp.post({ url: Api.deleteAssignmentUser, params });

//获取房租收缴配置列表
export const getRentCollectionConfigList = (params?) => defHttp.post({ url: Api.getRentCollectionConfigList, params });

//获取房租收缴配置详情
export const getRentCollectionConfigById = (params) => defHttp.post({ url: Api.getRentCollectionConfigById, params });

//删除房租收缴配置
export const deleteRentCollectionConfigById = (params) =>
  defHttp.post({ url: Api.deleteRentCollectionConfigById, params });

//修改房租收缴配置
export const updateRentCollectionConfig = (params) => defHttp.post({ url: Api.updateRentCollectionConfig, params });

//新增房租收缴配置
export const addRentCollectionConfig = (params) => defHttp.post({ url: Api.addRentCollectionConfig, params });

//设置任务状态
export const setTaskStatus = (params) => defHttp.post({ url: Api.setTaskStatus, params });
//获取关闭任务信息
export const getCloseTaskMsg = (params) => defHttp.post({ url: Api.getCloseTaskMsg, params });
//新增缴费
export const addTypePayment = (params) => defHttp.post({ url: Api.addTypePayment, params });

//获取合同借阅列表
export const getLeaseLendList = (params) => defHttp.post({ url: Api.getLeaseLendList, params });

//获取合同借阅详情
export const getLeaseLendById = (params) => defHttp.post({ url: Api.getLeaseLendById, params });

//删除合同借阅
export const deleteLeaseLendById = (params) => defHttp.post({ url: Api.deleteLeaseLendById, params });

//修改合同借阅
export const updateLeaseLend = (params) => defHttp.post({ url: Api.updateLeaseLend, params });

//新增合同借阅
export const addLeaseLend = (params) => defHttp.post({ url: Api.addLeaseLend, params });
//归还合同借阅
export const returnLeaseLend = (params) => defHttp.post({ url: Api.returnLeaseLend, params });
//充值
export const rechargeAdd = (params) => defHttp.post({ url: Api.rechargeAdd, params });
//获取验房详情
export const getCheckHouseInfo = (params) => defHttp.post({ url: Api.getCheckHouseInfo, params });
//合同续约
export const setNewEndTime = (params) => defHttp.post({ url: Api.setNewEndTime, params });
//合同中止
export const setStopTime = (params) => defHttp.post({ url: Api.setStopTime, params });
//获取充值列表
export const getRechargeList = (params) => defHttp.post({ url: Api.getRechargeList, params });
//日期修正
export const setNewTime = (params) => defHttp.post({ url: Api.setNewTime, params });
//设置展示时间
export const setNewShowTime = (params) => defHttp.post({ url: Api.setNewShowTime, params });

//更新月租金额
export const updateLeaseMonthAmount = (params) => {
  const { houseId, leaseId, monthAmount } = params;
  return defHttp.post({
    url: Api.updateLeaseMonthAmount
      .replace('{houseId}', houseId)
      .replace('{leaseId}', leaseId)
      .replace('{monthAmount}', monthAmount),
  });
};
//同步租户姓名
export const syncTenantName = (params) => defHttp.post({ url: Api.syncTenantName, params });
