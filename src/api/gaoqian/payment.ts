import { defHttp } from '@/utils/http/axios';

//缴费单
enum Api {
  getPaymentSlipsList = '/payment/paymentSlips/list',
  getMyPaymentSlipsList = '/payment/paymentSlips/my/list',
  getPaymentSlipsById = '/payment/paymentSlips/{id}',
  deletePaymentSlipsById = '/payment/paymentSlips/delete/{id}',
  updatePaymentSlips = '/payment/paymentSlips/edit',
  isExistSlips = '/payment/paymentSlips/isExistSlips/{id}',
  addPaymentSlips = '/payment/paymentSlips',

  commitPaymentSlips = '/payment/paymentSlips/commit',
  getArrearsById = '/payment/paymentSlips/lease/{leaseId}/arrears',
  //获取缴费单打印信息
  getPaymentSlipsPrintInfo = '/payment/paymentSlips/printInfo/{id}',
  //作废缴费单
  cancelPaymentSlips = '/payment/paymentSlips/zuofei/{leaseId}/{id}',
}

//获取缴费单列表
export const getPaymentSlipsList = (params) => defHttp.post({ url: Api.getPaymentSlipsList, params });
//获取我的缴费单列表
export const getMyPaymentSlipsList = (params) => defHttp.post({ url: Api.getMyPaymentSlipsList, params });

//获取缴费单详情
export const getPaymentSlipsById = (params) => defHttp.post({ url: Api.getPaymentSlipsById, params });

//删除缴费单
export const deletePaymentSlipsById = (params) => defHttp.post({ url: Api.deletePaymentSlipsById, params });

//修改缴费单
export const updatePaymentSlips = (params) => defHttp.post({ url: Api.updatePaymentSlips, params });

//新增缴费单
export const addPaymentSlips = (params) => defHttp.post({ url: Api.addPaymentSlips, params });

//提交缴费单
export const commitPaymentSlips = (params) => defHttp.post({ url: Api.commitPaymentSlips, params });

//判断是否存在缴费单
export const isExistSlips = (params) => defHttp.post({ url: Api.isExistSlips, params });
//获取欠费信息
export const getArrearsById = (params) => defHttp.post({ url: Api.getArrearsById, params });
//获取缴费单打印信息
export const getPaymentSlipsPrintInfo = (params) => defHttp.post({ url: Api.getPaymentSlipsPrintInfo, params });
//作废缴费单
export const cancelPaymentSlips = (params) => defHttp.post({ url: Api.cancelPaymentSlips, params });
