import { defHttp } from '@/utils/http/axios';

//租户信息
enum Api {
  //租户
  getTenantTableList = '/system/tenantTable/list',
  getTenantTableById = '/system/tenantTable/{id}',
  deleteTenantTableById = '/system/tenantTable/delete/{id}',
  updateTenantTable = '/system/tenantTable/edit',
  addTenantTable = '/system/tenantTable',
  //院落
  getCourtyardInfoList = '/system/courtyardInfo/list',
  getCourtyardInfoById = '/system/courtyardInfo/{id}',
  deleteCourtyardInfoById = '/system/courtyardInfo/delete/{id}',
  updateCourtyardInfo = '/system/courtyardInfo/edit',
  addCourtyardInfo = '/system/courtyardInfo',
  getCourtyardInfoTreeList = '/system/courtyardInfo/getTreeList',
  getCourtyardInfoParamTreeList = '/system/courtyardInfo/getParamTreeList',
  //楼栋
  getBuildingInfoList = '/system/buildingInfo/list',
  getBuildingInfoById = '/system/buildingInfo/{id}',
  deleteBuildingInfoById = '/system/buildingInfo/delete/{id}',
  updateBuildingInfo = '/system/buildingInfo/edit',
  addBuildingInfo = '/system/buildingInfo',

  getLeaseInfoList = '/gzf/leaseInfo/list',
  getLeaseInfoById = '/gzf/leaseInfo/{id}',
  deleteLeaseInfoById = '/gzf/leaseInfo/{id}',
  updateLeaseInfo = '/gzf/leaseInfo/edit',
  addLeaseInfo = '/gzf/leaseInfo',

  getCloseLeaseList = '/gzf/closeLease/list',
  getCloseLeaseById = '/gzf/closeLease/{id}',
  deleteCloseLeaseById = '/gzf/closeLease/{id}',
  updateCloseLease = '/gzf/closeLease/edit',
  addCloseLease = '/gzf/closeLease',

  getHouseBasicInfoList = '/system/houseBasicInfo/list',
  getHouseBasicInfoById = '/system/houseBasicInfo/{id}',
  deleteHouseBasicInfoById = '/system/houseBasicInfo/delete/{id}',
  updateHouseBasicInfo = '/system/houseBasicInfo/edit',
  addHouseBasicInfo = '/system/houseBasicInfo',
  changeHouseStatus = '/system/houseBasicInfo/statusChange/{id}/{status}',
  getHouseStatistics = '/system/houseBasicInfo/housingStatistics',
  getHouseDataStatustics = '/system/houseBasicInfo/houseDataStatusStatistics',
  setEnableStatus = '/system/houseBasicInfo/enable/{id}/{enableStatus}',
  houseRent = '/gzf/domain/rent',
  getNoPayment = '/gzf/payment/{id}/0/list',
  getLeasePdfInfo = '/contract/getInfo/{id}',
  generateKey = '/contract/generateKey',
  generateInfo = '/contract/getData/{key}',
  generateLink = '/contract/regenerateLinks/{id}',
  regenerateLinks = '/gzf/domain/restart/tempLink',
  //合约打印
  getLeasePrintList = '/gzf/leasePrint/list',
  getLeasePrintById = '/gzf/leasePrint/{id}',
  deleteLeasePrintById = '/gzf/leasePrint/{id}',
  updateLeasePrint = '/gzf/leasePrint/edit',
  addLeasePrint = '/gzf/leasePrint',

  getUserByHouseId = '/system/collectionHouse/getCollectorInfo/{houseId}',
  updateCollector = '/system/collectionHouse/update/{houseId}/{feeCollectorId}',

  //空置院落台账
  getKongzhiYuanluoList = '/gzf/report/kongzhi/yuanluo',
  //空置台账内部
  getKongzhiYuanluoById = '/gzf/report/kongzhi/yuanluo/{userId}',

  getFeeOption = '/system/feeOption/list',
  addFeeConfig = '/system/communityFeeConfig',
  getFeeConfigById = '/system/communityFeeConfig/{id}',
}

export const setEnableStatus = (params) => defHttp.post({ url: Api.setEnableStatus, params });

//获取租户信息列表
export const getTenantTableList = (params) => defHttp.post({ url: Api.getTenantTableList, params });

//获取租户信息详情
export const getTenantTableById = (params) => defHttp.post({ url: Api.getTenantTableById, params });

//删除租户信息
export const deleteTenantTableById = (params) => defHttp.post({ url: Api.deleteTenantTableById, params });

//修改租户信息
export const updateTenantTable = (params) => defHttp.post({ url: Api.updateTenantTable, params });

//新增租户信息
export const addTenantTable = (params) => defHttp.post({ url: Api.addTenantTable, params });

export const getCourtyardInfoTreeList = (params?) => defHttp.post({ url: Api.getCourtyardInfoTreeList, params });
export const getCourtyardInfoParamTreeList = (params?) =>
  defHttp.post({ url: Api.getCourtyardInfoParamTreeList, params });

//获取院落基本信息列表
export const getCourtyardInfoList = (params?) => defHttp.post({ url: Api.getCourtyardInfoList, params });

//获取院落基本信息详情
export const getCourtyardInfoById = (params) => defHttp.post({ url: Api.getCourtyardInfoById, params });

//删除院落基本信息
export const deleteCourtyardInfoById = (params) => defHttp.post({ url: Api.deleteCourtyardInfoById, params });

//修改院落基本信息
export const updateCourtyardInfo = (params) => defHttp.post({ url: Api.updateCourtyardInfo, params });

//新增院落基本信息
export const addCourtyardInfo = (params) => defHttp.post({ url: Api.addCourtyardInfo, params });

//获取楼栋信息列表
export const getBuildingInfoList = (params?) => defHttp.post({ url: Api.getBuildingInfoList, params });

//获取楼栋信息详情
export const getBuildingInfoById = (params) => defHttp.post({ url: Api.getBuildingInfoById, params });

//删除楼栋信息
export const deleteBuildingInfoById = (params) => defHttp.post({ url: Api.deleteBuildingInfoById, params });

//修改楼栋信息
export const updateBuildingInfo = (params) => defHttp.post({ url: Api.updateBuildingInfo, params });

//新增楼栋信息
export const addBuildingInfo = (params) => defHttp.post({ url: Api.addBuildingInfo, params });

//获取合同信息列表
export const getLeaseInfoList = (params) => defHttp.post({ url: Api.getLeaseInfoList, params });

//获取合同信息详情
export const getLeaseInfoById = (params) => defHttp.post({ url: Api.getLeaseInfoById, params });

//删除合同信息
export const deleteLeaseInfoById = (params) => defHttp.post({ url: Api.deleteLeaseInfoById, params });

//修改合同信息
export const updateLeaseInfo = (params) => defHttp.post({ url: Api.updateLeaseInfo, params });

//新增合同信息
export const addLeaseInfo = (params) => defHttp.post({ url: Api.addLeaseInfo, params });

//获取房屋基本信息列表
export const getHouseBasicInfoList = (params) => defHttp.post({ url: Api.getHouseBasicInfoList, params });

//获取房屋基本信息详情
export const getHouseBasicInfoById = (params) => defHttp.post({ url: Api.getHouseBasicInfoById, params });

//删除房屋基本信息
export const deleteHouseBasicInfoById = (params) => defHttp.post({ url: Api.deleteHouseBasicInfoById, params });

//修改房屋基本信息
export const updateHouseBasicInfo = (params) => defHttp.post({ url: Api.updateHouseBasicInfo, params });

//新增房屋基本信息
export const addHouseBasicInfo = (params) => defHttp.post({ url: Api.addHouseBasicInfo, params });
//修改房屋状态
export const changeHouseStatus = (params) => defHttp.post({ url: Api.changeHouseStatus, params });

//出租房屋
export const houseRent = (params) => defHttp.post({ url: Api.houseRent, params });
//查询未缴费
export const getNoPayment = (params) => defHttp.get({ url: Api.getNoPayment, params });

export const getHouseStatistics = (params?) => defHttp.post({ url: Api.getHouseStatistics, params });
export const getHouseDataStatustics = (params?) => defHttp.post({ url: Api.getHouseDataStatustics, params });

//获取结束合同列表
export const getCloseLeaseList = (params) => defHttp.post({ url: Api.getCloseLeaseList, params });
//获取结束合同详情
export const getCloseLeaseById = (params) => defHttp.post({ url: Api.getCloseLeaseById, params });
//删除结束合同
export const deleteCloseLeaseById = (params) => defHttp.post({ url: Api.deleteCloseLeaseById, params });
//修改结束合同
export const updateCloseLease = (params) => defHttp.post({ url: Api.updateCloseLease, params });
//新增结束合同
export const addCloseLease = (params) => defHttp.post({ url: Api.addCloseLease, params });
//获取合同pdf信息
export const getLeasePdfInfo = (params) => defHttp.get({ url: Api.getLeasePdfInfo, params });
//生成合同key
export const generateKey = (params) => defHttp.post({ url: Api.generateKey, params });

export const geterateLink = (params) => defHttp.post({ url: Api.generateLink, params });

//重新生成合同链接
export const regenerateLinks = (params) => defHttp.post({ url: Api.regenerateLinks, params });

export const generateInfo = (params) => defHttp.get({ url: Api.generateInfo, params });

//获取合约打印列表
export const getLeasePrintList = (params) => defHttp.post({ url: Api.getLeasePrintList, params });

//获取合约打印详情
export const getLeasePrintById = (params) => defHttp.post({ url: Api.getLeasePrintById, params });

//删除合约打印
export const deleteLeasePrintById = (params) => defHttp.post({ url: Api.deleteLeasePrintById, params });

//修改合约打印
export const updateLeasePrint = (params) => defHttp.post({ url: Api.updateLeasePrint, params });

//新增合约打印
export const addLeasePrint = (params) => defHttp.post({ url: Api.addLeasePrint, params });
//获取收款人信息
export const getUserByHouseId = (params) => defHttp.post({ url: Api.getUserByHouseId, params });
//updateCollector
export const updateCollector = (params) => defHttp.post({ url: Api.updateCollector, params });
//空置院落台账
export const getKongzhiYuanluoList = (params) => defHttp.post({ url: Api.getKongzhiYuanluoList, params });
//空置台账内部
export const getKongzhiYuanluoById = (params) => defHttp.post({ url: Api.getKongzhiYuanluoById, params });
//获取收费项
export const getFeeOption = (params?) => defHttp.post({ url: Api.getFeeOption, params });
//新增收费配置
export const addFeeConfig = (params) => defHttp.post({ url: Api.addFeeConfig, params });
//获取收费配置详情
export const getFeeConfigById = (params) => defHttp.post({ url: Api.getFeeConfigById, params });

export const removeSettlement = ({ leaseId, houseId }) => {
  return defHttp.post({ url: `/system/settlementReturn/removejiesuan/${leaseId}/${houseId}` });
};
