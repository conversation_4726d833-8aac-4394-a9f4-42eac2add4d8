import { defHttp } from '@/utils/http/axios';

//押金记录
enum Api {
  getDepositRecordList = '/gzf/depositRecord/list',
  getDepositRecordById = '/gzf/depositRecord/{id}',
  deleteDepositRecordById = '/gzf/depositRecord/{id}',
  updateDepositRecord = '/gzf/depositRecord/edit',
  addDepositRecord = '/gzf/depositRecord',
}

//获取押金记录列表
export const getDepositRecordList = (params) => defHttp.post({ url: Api.getDepositRecordList, params });

//获取押金记录详情
export const getDepositRecordById = (params) => defHttp.post({ url: Api.getDepositRecordById, params });

//删除押金记录
export const deleteDepositRecordById = (params) => defHttp.post({ url: Api.deleteDepositRecordById, params });

//修改押金记录
export const updateDepositRecord = (params) => defHttp.post({ url: Api.updateDepositRecord, params });

//新增押金记录
export const addDepositRecord = (params) => defHttp.post({ url: Api.addDepositRecord, params });
