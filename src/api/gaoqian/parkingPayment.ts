import { defHttp } from '@/utils/http/axios';

enum Api {
  getParkingPaymentDetail = '/payment/paymentSlipsDetails/list',
  getParkingPaymentList = '/gzf/parkingPayment/list',
  //作废停车缴费记录
  cancelParkingPayment = '/gzf/parkingPayment/delete/{id}',
}

/**
 * 获取停车缴费记录列表
 * @param params 请求参数
 * @returns 停车缴费记录列表
 */
export const getParkingPaymentList = (params) =>
  defHttp.post({
    url: Api.getParkingPaymentList,
    params,
  });

//作废停车缴费记录
export const cancelParkingPayment = (params) =>
  defHttp.post({
    url: Api.cancelParkingPayment,
    params,
  });

//获取停车缴费记录详情
export const getParkingPaymentDetail = (params) =>
  defHttp.post({
    url: Api.getParkingPaymentDetail,
    params,
  });
