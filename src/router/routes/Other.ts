import { LAYOUT } from '../constant';
//需要登录后才能进入切需要权限的页面放这里
export const OtherRoutes = {
  path: '/other',
  name: 'Other',
  component: LAYOUT,
  children: [
    {
      path: 'error-log',
      name: 'errorLog',
      component: () => import('@/views/main/other/errorLog/index.vue'),
      meta: {
        title: '错误日志',
      },
    },
    // {
    //   path: 'analysis',
    //   name: 'Analysis',
    //   component: () => import('@/views/main/other/analysis/index.vue'),
    //   meta: {
    //     title: '主页',
    //     affix: true,
    //   },
    // },
    {
      path: 'userCenter',
      name: 'UserCenter',
      component: () => import('@/views/main/other/userCenter/index.vue'),
      meta: {
        title: '个人中心',
      },
    },
    {
      path: 'changePassword',
      name: 'ChangePassword',
      component: () => import('@/views/main/other/password/index.vue'),
      meta: {
        title: '修改密码',
      },
    },
    {
      path: 'genTool',
      name: 'GenTool',
      component: () => import('@/views/main/other/gen/index.vue'),
      meta: {
        title: '代码生成',
      },
    },
    {
      path: 'genTool/:id',
      name: 'GenToolDetail',
      component: () => import('@/views/main/other/gen/GenEdit.vue'),
      meta: {
        title: '代码生成',
      },
    },
    {
      path: 'workbench',
      name: 'Workbench',
      component: () => import('@/views/main/other/workbench/index.vue'),
      meta: {
        title: '工作台',
        // affix: true,
      },
    },
    {
      path: 'workbench-api-test',
      name: 'WorkbenchApiTest',
      component: () => import('@/views/main/other/workbench/api-test.vue'),
      meta: {
        title: '工作台API测试',
      },
    },
    {
      path: 'analytics',
      name: 'Analytics',
      component: () => import('@/views/dashboard/analytics.vue'),
      meta: {
        title: '数据分析仪表板',
      },
    },
    {
      path: 'analytics-test',
      name: 'AnalyticsTest',
      component: () => import('@/views/dashboard/analytics-test.vue'),
      meta: {
        title: '仪表板测试',
      },
    },
  ],
  meta: {
    title: '其他配置',
  },
};
