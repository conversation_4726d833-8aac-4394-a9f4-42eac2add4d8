{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "yy", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 35724834, "auth": {"type": "bearer", "bearer": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMV2R6aWVQYmtVdVp1alZwSzJ5VFJycWRvaVRGWmZGbyIsInVzZXJJZCI6MX0.uMocPsxzBkhFG6MIhECcVfpd-9HxzUmgOM_CKQCu7G4"}}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": ""}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 5544862, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "工作台统计2（小区权限类）", "id": 63105528, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5544862, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "收款类别占比统计", "api": {"id": "330351053", "method": "post", "path": "/gzf/workTablePermission/collectionType", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "735384656", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/102048011"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "收款类别占比统计", "tags": ["小区工作台控制器"], "status": "developing", "serverId": "", "operationId": "collectionType_1", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.735384656"], "visibility": "INHERITED", "moduleId": 5544862, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "房屋状态统计", "api": {"id": "330351434", "method": "post", "path": "/gzf/workTablePermission/houseStatusStatistics", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "735387841", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/123817804"}, "description": "房屋状态统计结果", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"$ref": "#/definitions/102106610"}, "examples": [{"value": "", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "房屋状态统计", "tags": ["小区工作台控制器"], "status": "developing", "serverId": "", "operationId": "houseStatusStatistics", "sourceUrl": "", "ordering": 6, "cases": [{"id": 287581973, "type": "http", "path": null, "name": "成功", "responseId": 735387841, "parameters": {"query": [], "path": [], "cookie": [], "header": []}, "commonParameters": {"query": [], "body": [], "header": [], "cookie": []}, "requestBody": {"parameters": [], "type": "application/json", "data": "{}", "generateMode": "normal"}, "auth": {}, "securityScheme": {}, "advancedSettings": {"disabledSystemHeaders": {}}, "requestResult": null, "visibility": "INHERITED", "moduleId": 5544862, "categoryId": 0, "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.735387841"], "visibility": "INHERITED", "moduleId": 5544862, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "工作台-数值数据统计", "api": {"id": "330351768", "method": "post", "path": "/gzf/workTablePermission/numericalDataStatistics", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "735384658", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/117919310"}, "description": "数值数据统计结果", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "工作台-数值数据统计", "tags": ["小区工作台控制器"], "status": "developing", "serverId": "", "operationId": "queryNumericalDataStatistics", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": ["BLANK.735384658"], "visibility": "INHERITED", "moduleId": 5544862, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "未缴费 统计-3 6 9 12月", "api": {"id": "333488433", "method": "post", "path": "/gzf/workTable/notPaymentMonthTime", "parameters": {"path": [], "query": [], "cookie": [], "header": []}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "740775104", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"$ref": "#/definitions/117919310"}, "description": "OK", "contentType": "json", "mediaType": "*/*", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "none", "parameters": [], "oasExtensions": ""}, "description": "未缴费 统计-3 6 9 12月", "tags": ["工作台控制器"], "status": "released", "serverId": "", "operationId": "queryNumericalDataStatisticsPermission", "sourceUrl": "", "ordering": 24, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5544862, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 4645609, "updatedAt": "2024-06-10T03:09:59.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 5544862, "parentId": 0, "id": 4645609, "ordering": [], "items": []}], "schemaCollection": [{"id": 9394593, "name": "根目录", "visibility": "SHARED", "moduleId": 5544862, "items": [{"id": 9394597, "name": "<PERSON><PERSON><PERSON>", "visibility": "INHERITED", "moduleId": 5544862, "items": [{"name": "RMapStringString", "displayName": "", "id": "#/definitions/102048011", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"type": "string"}, "x-apifox-orders": []}}, "description": "响应信息主体", "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 5544862}, {"name": "GzfHouseBasicInfoBo", "displayName": "", "id": "#/definitions/102106610", "description": "房屋基本信息业务对象 gzf_house_basic_info", "schema": {"jsonSchema": {"required": ["buildId", "courtyardId", "floor", "id", "unit"], "type": "object", "properties": {"params": {"type": "object", "additionalProperties": {"type": "object", "x-apifox-orders": []}, "description": "请求参数", "x-apifox-orders": []}, "createBy": {"type": "string", "description": "创建者"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "updateBy": {"type": "string", "description": "更新者"}, "updateTime": {"type": "string", "description": "更新时间", "format": "date-time"}, "id": {"type": "integer", "description": "id", "format": "int64"}, "roomNumber": {"type": "string", "description": "房号"}, "buildingArea": {"type": "number", "description": "建筑面积"}, "usableArea": {"type": "number", "description": "实用面积"}, "commonArea": {"type": "number", "description": "公摊面积"}, "layout": {"type": "string", "description": "户型"}, "image": {"type": "array", "description": "图片链接", "items": {"type": "string"}}, "floor": {"type": "integer", "description": "楼层", "format": "int64"}, "unit": {"type": "string", "description": "单元"}, "rentalStatus": {"type": "string", "description": "出租状态"}, "leaseStatus": {"type": "string", "description": "租约状态"}, "buildId": {"type": "integer", "description": "楼栋id", "format": "int64"}, "courtyardId": {"type": "integer", "description": "院落id", "format": "int64"}, "courtyardIds": {"type": "array", "description": "院落集合", "items": {"type": "integer", "format": "int64"}}, "houseStatus": {"type": "string", "description": "房屋状态"}, "enable": {"type": "string", "description": "数据状态"}, "propertyRights": {"type": "string", "description": "产权性质"}, "decorationInfo": {"type": "string", "description": "装修信息"}, "referenceMonthlyRent": {"type": "number", "description": "参考月租"}, "costPrice": {"type": "number", "description": "成本价"}, "houseType": {"type": "string", "description": "房屋类型"}, "paymentOptions": {"type": "string", "description": "缴费选项"}, "lastRentalEndTime": {"type": "string", "description": "上次出租结束时间 last_rental_end_time", "format": "date"}, "tingjiFlag": {"type": "string", "description": "是否厅级周转房"}}, "description": "房屋基本信息业务对象 gzf_house_basic_info", "x-apifox-orders": ["params", "createBy", "createTime", "updateBy", "updateTime", "id", "roomNumber", "buildingArea", "usableArea", "commonArea", "layout", "image", "floor", "unit", "rentalStatus", "leaseStatus", "buildId", "courtyardId", "courtyardIds", "houseStatus", "enable", "propertyRights", "decorationInfo", "referenceMonthlyRent", "costPrice", "houseType", "paymentOptions", "lastRentalEndTime", "tingjiFlag"]}}, "visibility": "INHERITED", "moduleId": 5544862}, {"name": "RListHouseDataStatus", "displayName": "", "id": "#/definitions/123817804", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/123817803"}}}, "description": "响应信息主体", "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 5544862}, {"name": "HouseDataStatus", "displayName": "", "id": "#/definitions/123817803", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "integer", "format": "int64"}}, "description": "", "x-apifox-orders": ["name", "value"]}}, "visibility": "INHERITED", "moduleId": 5544862}, {"name": "RListNameValue", "displayName": "", "id": "#/definitions/117919310", "description": "响应信息主体", "schema": {"jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/117919309"}}}, "description": "响应信息主体", "x-apifox-orders": ["code", "msg", "data"]}}, "visibility": "INHERITED", "moduleId": 5544862}, {"name": "NameValue", "displayName": "", "id": "#/definitions/117919309", "description": "", "schema": {"jsonSchema": {"type": "object", "properties": {"key": {"type": "string"}, "keyView": {"type": "string"}, "value": {"type": "number"}, "percentage": {"type": "string"}}, "description": "", "x-apifox-orders": ["key", "<PERSON><PERSON><PERSON><PERSON>", "value", "percentage"]}}, "visibility": "INHERITED", "moduleId": 5544862}]}]}], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.4825973"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [], "moduleVariables": [{"id": "5544862", "variables": []}], "commonParameters": {"id": 598582, "createdAt": "2024-06-12T14:20:26.000Z", "updatedAt": "2024-06-12T14:20:26.000Z", "deletedAt": null, "parameters": {"header": [{"name": "dev", "defaultEnable": true, "type": "string", "id": "sYWGDl0qET", "defaultValue": "userName=admin;replace=true", "schema": {"type": "string", "default": "userName=admin;replace=true"}}]}, "projectId": 4632682, "creatorId": 1847465, "editorId": 1847465}, "projectSetting": {"id": "4642614", "auth": {"type": "bearer", "bearer": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJMV2R6aWVQYmtVdVp1alZwSzJ5VFJycWRvaVRGWmZGbyIsInVzZXJJZCI6MX0.uMocPsxzBkhFG6MIhECcVfpd-9HxzUmgOM_CKQCu7G4"}}, "securityScheme": {}, "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "isDefaultUrlEncoding": 2, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "LEGACY", "enableResourceKeyStandard": true}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}}, "initialDisabledMockIds": [], "servers": [{"id": "default", "name": "默认服务"}], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}