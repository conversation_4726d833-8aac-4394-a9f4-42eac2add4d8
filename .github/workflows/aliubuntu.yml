name: ali_ubuntu
on: workflow_dispatch

jobs:
  build:
    runs-on: [self-hosted, Linux,ali]
    # strategy:
    #   matrix:
    #     node-version: [14.x, 16.x, 18.x]
    #     # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      # - name: 代理
      #   run: |
      #     echo $PATH &
      #     export http_proxy=http://************:7890 &
      #     export https_proxy=http://************:7890
      - uses: actions/checkout@v4
      # - name: Use Node.js ${{ matrix.node-version }}
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: ${{ matrix.node-version }}
      #     cache: 'npm'
      # - name: Setup PATH for npm
      #   run: echo "/home/<USER>/.nvm/versions/node/v20.15.0/bin" >> $GITHUB_PATH
      # - name: 测试
      #   run: |
      #    whoami &
      #    echo $PATH
      # - name: 安装pnpm
      #   run: npm install pnpm -g
      - name: 安装依赖
        run: pnpm install
      - name: build
        run: pnpm build
      - name: 部署到nginx
        run: cp -rf /home/<USER>/actions-runner/_work/xxx/xxx/dist/* ~/web/xx/
  # deploy:
  #   needs: build
  #   runs-on: [self-hosted,Linux, ali]
  #   steps:
  #     - name: 部署到nginx
  #       run: cp -rf /home/<USER>/actions-runner/_work/xxx/xxx/dist/* ~/web/xx/
