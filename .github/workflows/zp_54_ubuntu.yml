name: zp_54_ubuntu
on: workflow_dispatch

jobs:
  build:
    runs-on: [self-hosted, Linux]
   

    steps:
      - uses: actions/checkout@v4
      - name: Setup PATH for npm
        run: echo "/home/<USER>/.nvm/versions/node/v20.18.0/bin" >> $GITHUB_PATH
      - name: 安装依赖
        run: pnpm install
      - name: build
        run: pnpm build
  deploy:
    needs: build
    runs-on: [self-hosted,Linux]
    steps:
      - name: 部署到nginx
        run: cp -rf /home/<USER>/actions-runner/_work/xxx/xxx/dist/* /html
