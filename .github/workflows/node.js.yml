# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: 阿里云服务器
on: workflow_dispatch

jobs:
  build:
    runs-on: [self-hosted, Windows]
    # strategy:
    #   matrix:
    #     node-version: [14.x, 16.x, 18.x]
    #     # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v4
      # - name: Use Node.js ${{ matrix.node-version }}
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: ${{ matrix.node-version }}
      #     cache: 'npm'
      - name: 安装
        run: npm install pnpm -g
      - name: 安装依赖
        run: pnpm install
      - name: build
        run: pnpm build

  deploy:
    needs: build
    runs-on: [self-hosted, Windows]
    steps:
      # - name: 归档旧文件

      - name: 部署到nginx
        run: Copy-Item -Path 'C:\runner\_work\xx\xx\dist\*' -Destination 'C:\nginx-1.25.5\xx' -Recurse -Force
