# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: 笔记本编译
on: workflow_dispatch

jobs:
  build:
    runs-on: [self-hosted, Windows]
    # strategy:
    #   matrix:
    #     node-version: [14.x, 16.x, 18.x]
    #     # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    # env:
    #   MY_SECRET: ${{ secrets.SERVICE_PWD }}
    steps:
      - uses: actions/checkout@v4
      - name: 安装
        run: npm install pnpm -g
      - name: 安装依赖
        run: pnpm install
      - name: build
        run: pnpm build
      - name: 打包dist
        run: cd ./dist; tar -cf C:\dist\dist.tar .
      - name: 上传服务器
        run: scp C:\dist\dist.tar root@*************:/home/<USER>/
        
      # 在这个电脑上测试了好多次，使用scp一直没有反应 所以用git
      # - name: 上传到gitee
      #   run:  cd  C:\dist\ ;git add . ;git commit -m "update" ; git push -u origin "master"

  deploy:
    needs: build
    runs-on: [self-hosted, linux]
    steps:
      - name: 解压并部署
        run: tar -xvf /home/<USER>/dist.tar -C /home/<USER>/xx
      # - name: 部署到nginx
      #   run:  mv /home/<USER>/dist/* /home/<USER>/xx/
