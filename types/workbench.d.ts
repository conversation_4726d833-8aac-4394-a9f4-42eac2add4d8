// 工作台相关接口类型定义

// 通用响应结构
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 名称-值对象
export interface NameValue {
  key: string;
  keyView: string;
  value: number;
  percentage: string;
}

// 房屋状态数据
export interface HouseDataStatus {
  name: string;
  value: number;
}

// 房屋基本信息业务对象
export interface GzfHouseBasicInfoBo {
  params?: Record<string, any>;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  id: number;
  roomNumber?: string;
  buildingArea?: number;
  usableArea?: number;
  commonArea?: number;
  layout?: string;
  image?: string[];
  floor: number;
  unit: string;
  rentalStatus?: string;
  leaseStatus?: string;
  buildId: number;
  courtyardId: number;
  courtyardIds?: number[];
  houseStatus?: string;
  enable?: string;
  propertyRights?: string;
  decorationInfo?: string;
  referenceMonthlyRent?: number;
  costPrice?: number;
  houseType?: string;
  paymentOptions?: string;
  lastRentalEndTime?: string;
  tingjiFlag?: string;
}

// 工作台接口响应类型
export type CollectionTypeResponse = ApiResponse<Record<string, string>>;
export type HouseStatusStatisticsResponse = ApiResponse<HouseDataStatus[]>;
export type NumericalDataStatisticsResponse = ApiResponse<NameValue[]>;
export type NotPaymentMonthTimeResponse = ApiResponse<NameValue[]>;

// 工作台统计数据类型
export interface WorkbenchStatistics {
  collectionType?: Record<string, string>;
  houseStatusStatistics?: HouseDataStatus[];
  numericalDataStatistics?: NameValue[];
  notPaymentMonthTime?: NameValue[];
}
