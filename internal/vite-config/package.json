{"name": "@vben/vite-config", "version": "1.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/vite-config"}, "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint .", "stub": "pnpm unbuild --stub"}, "dependencies": {"@ant-design/colors": "^7.0.2", "vite": "^5.1.3"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "ant-design-vue": "^4.1.2", "cors": "^2.8.5", "dayjs": "^1.11.10", "dotenv": "^16.4.4", "etag": "^1.8.1", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "less": "^4.2.0", "pathe": "^1.1.2", "picocolors": "^1.0.0", "pkg-types": "^1.0.3", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.71.0", "svg-baker": "^1.7.0", "unocss": "^0.58.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^3.7.2", "vite-plugin-html": "^3.2.2"}}