# 工作台API对接说明

## 概述

本次对接了yy.apifox.json中定义的4个工作台相关API接口，用于获取小区权限类的统计数据。

## 已对接的API接口

### 1. 收款类别占比统计

- **接口路径**: `/gzf/workTablePermission/collectionType`
- **请求方法**: POST
- **函数名**: `getPermissionCollectionType`
- **返回类型**: `CollectionTypeResponse`
- **说明**: 获取收款类别的占比统计数据

### 2. 房屋状态统计

- **接口路径**: `/gzf/workTablePermission/houseStatusStatistics`
- **请求方法**: POST
- **函数名**: `getPermissionHouseStatusStatistics`
- **返回类型**: `HouseStatusStatisticsResponse`
- **说明**: 获取房屋状态的统计数据

### 3. 工作台-数值数据统计

- **接口路径**: `/gzf/workTablePermission/numericalDataStatistics`
- **请求方法**: POST
- **函数名**: `getPermissionNumericalDataStatistics`
- **返回类型**: `NumericalDataStatisticsResponse`
- **说明**: 获取工作台的数值统计数据

### 4. 未缴费统计-3 6 9 12月

- **接口路径**: `/gzf/workTable/notPaymentMonthTime`
- **请求方法**: POST
- **函数名**: `getNotPaymentMonthTime`
- **返回类型**: `NotPaymentMonthTimeResponse`
- **说明**: 获取3、6、9、12个月的未缴费统计数据

## 文件修改说明

### 1. API函数定义

- **文件**: `src/api/main/other.ts`
- **修改内容**:
  - 添加了4个新的API枚举值
  - 添加了4个对应的API调用函数
  - 添加了TypeScript类型注解

### 2. 类型定义

- **文件**: `types/workbench.d.ts`
- **内容**: 定义了所有相关的TypeScript接口类型

### 3. 工作台页面更新

- **文件**: `src/views/main/other/workbench/manage.vue`
- **修改内容**:
  - 导入新的API函数
  - 添加响应式数据管理
  - 实现API数据加载逻辑
  - 更新模板数据绑定
  - 添加数据刷新功能

### 4. API测试页面

- **文件**: `src/views/main/other/workbench/api-test.vue`
- **内容**: 提供API接口测试功能

### 5. 错误处理示例页面

- **文件**: `src/views/main/other/workbench/error-handling-example.vue`
- **内容**: 演示如何处理部分API失败的情况

### 6. 路由配置

- **文件**: `src/router/routes/Other.ts`
- **修改内容**: 添加API测试页面和错误处理示例页面路由

## 使用方法

### 1. 在组件中使用API

```typescript
import {
  getPermissionCollectionType,
  getPermissionHouseStatusStatistics,
  getPermissionNumericalDataStatistics,
  getNotPaymentMonthTime,
} from '@/api/main/other';

// 调用API
const loadData = async () => {
  try {
    const response = await getPermissionCollectionType();
    console.log('收款类别数据:', response.data);
  } catch (error) {
    console.error('API调用失败:', error);
  }
};
```

### 2. 访问测试页面

- **API测试**: `/other/workbench-api-test` - 测试所有API接口的调用和响应
- **错误处理示例**: `/other/workbench-error-handling` - 演示独立错误处理机制

### 3. 查看工作台页面

- 路径: `/other/workbench`
- 功能: 查看集成了真实API数据的工作台

## 数据结构说明

### 响应数据格式

**重要**: API直接返回数据，不包装在通用响应结构中。

```typescript
// API直接返回具体数据，例如：
// 收款类别: { "房租": "44.55", "物业费": "45.47" }
// 房屋状态: [{ name: "出售", value: 2590 }]
```

### 具体数据类型

- **收款类别**: `Record<string, string>` - 键值对形式的收款类别数据
- **房屋状态**: `HouseDataStatus[]` - 包含name和value的数组
- **数值统计**: `NameValue[]` - 包含key、keyView、value、percentage的数组
- **未缴费统计**: `NameValue[]` - 同数值统计格式

### API响应处理

```typescript
// 正确的处理方式
const response = await getPermissionCollectionType();
// response 直接就是数据，不需要 response.data

// 错误的处理方式
// const data = response.data; // ❌ 不需要这样
```

## 错误处理机制

### 独立错误处理

为了确保单个API接口报错不会影响整个数据加载过程，我们采用了独立错误处理机制：

```typescript
// 每个API调用都有独立的错误处理
const loadNumericalData = async () => {
  try {
    const response = await getPermissionNumericalDataStatistics();
    // 处理成功响应
    return response;
  } catch (error) {
    console.error('数值统计数据加载失败:', error);
    return null; // 返回null而不是抛出错误
  }
};

// 使用Promise.all并行调用，但各自独立处理错误
const [numericalResponse, houseStatusResponse, collectionTypeResponse, notPaymentResponse] = await Promise.all([
  loadNumericalData(),
  loadHouseStatusData(),
  loadCollectionTypeData(),
  loadNotPaymentData(),
]);
```

### 加载状态管理

- 全局加载状态：`loading`
- 单个API加载状态：`loadingStatus.numerical`、`loadingStatus.houseStatus`等
- 状态值：`idle`、`loading`、`success`、`error`

## 注意事项

1. **认证**: 所有API调用都会自动携带项目配置的认证信息
2. **错误处理**: 采用独立错误处理，单个接口失败不影响其他接口
3. **数据更新**: 工作台页面在组件挂载时会自动加载数据，也可以手动点击"刷新数据"按钮
4. **类型安全**: 所有API函数都有完整的TypeScript类型定义
5. **加载状态**: 提供了详细的加载状态指示，便于用户了解数据加载进度

## 测试建议

1. 首先访问API测试页面验证接口是否正常工作
2. 检查网络请求和响应数据格式
3. 确认工作台页面数据显示是否正确
4. 测试数据刷新功能

## 后续扩展

如需添加更多API接口，请按照以下步骤：

1. 在`src/api/main/other.ts`中添加API枚举和函数
2. 在`types/workbench.d.ts`中添加相应的类型定义
3. 在组件中导入并使用新的API函数
4. 更新测试页面以包含新的API测试
